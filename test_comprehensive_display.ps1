# اختبار العرض الشامل التفصيلي الجديد للدوال الـ36 والملفات الشاملة

Write-Host "🔥 بدء اختبار العرض الشامل التفصيلي الجديد..." -ForegroundColor Yellow

# التحقق من وجود الملفات المطلوبة
$requiredFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "test_comprehensive_display.js"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ الملف المطلوب غير موجود: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# إنشاء ملف HTML للاختبار
$testHTML = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار العرض الشامل التفصيلي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
        .progress-bar { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار العرض الشامل التفصيلي للدوال الـ36</h1>
        
        <div class="section">
            <h2>🧪 حالة الاختبار</h2>
            <div id="testStatus">⏳ جاري تحميل النظام...</div>
        </div>
        
        <div class="section">
            <h2>📊 نقاط الجودة</h2>
            <div id="qualityScore"></div>
        </div>
        
        <div class="section">
            <h2>🔍 تحليل العرض</h2>
            <div id="displayAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>📝 عينة من المحتوى</h2>
            <div id="contentSample"></div>
        </div>
        
        <div class="section">
            <h2>📋 تفاصيل الاختبار</h2>
            <div id="testDetails"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_comprehensive_display.js"></script>
    
    <script>
        async function runDisplayTest() {
            const statusDiv = document.getElementById('testStatus');
            
            try {
                statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل اختبار العرض الشامل...</div>';
                
                // تشغيل الاختبار
                const result = await testComprehensiveDisplay();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار العرض بنجاح!</div>';
                    
                    // عرض نقاط الجودة
                    displayQualityScore(result.qualityScore, result.analysis);
                    
                    // عرض تحليل العرض
                    displayAnalysis(result.analysis);
                    
                    // عرض عينة المحتوى
                    displayContentSample(result.sampleContent, result.fullContentSize);
                    
                    // عرض تفاصيل الاختبار
                    displayTestDetails(result);
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ فشل الاختبار: ' + result.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        function displayQualityScore(score, analysis) {
            const scoreDiv = document.getElementById('qualityScore');
            const percentage = (score / 8) * 100;
            
            let scoreClass = 'poor';
            let scoreText = 'ضعيف';
            
            if (percentage >= 87.5) {
                scoreClass = 'excellent';
                scoreText = 'ممتاز';
            } else if (percentage >= 62.5) {
                scoreClass = 'good';
                scoreText = 'جيد';
            } else if (percentage >= 37.5) {
                scoreClass = 'fair';
                scoreText = 'مقبول';
            }
            
            scoreDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${scoreClass}">${score}/8</div>
                        <div>نقاط الجودة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${scoreClass}">${percentage.toFixed(1)}%</div>
                        <div>النسبة المئوية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${scoreClass}">${scoreText}</div>
                        <div>التقييم العام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.functionsDisplayed}/17</div>
                        <div>الدوال المُطبقة</div>
                    </div>
                </div>
                
                <div class="progress">
                    <div class="progress-bar" style="width: ${percentage}%"></div>
                </div>
            `;
        }
        
        function displayAnalysis(analysis) {
            const analysisDiv = document.getElementById('displayAnalysis');
            
            const checks = [
                { name: 'بنية شاملة', value: analysis.hasComprehensiveStructure },
                { name: 'مجموعات الدوال', value: analysis.hasFunctionGroups },
                { name: 'جميع المجموعات الخمس', value: analysis.hasAllFiveGroups },
                { name: 'الملفات الشاملة', value: analysis.hasComprehensiveFiles },
                { name: 'ملخص النظام', value: analysis.hasSystemSummary },
                { name: 'تنسيق صحيح', value: analysis.hasProperFormatting },
                { name: 'بنية سليمة', value: analysis.isWellStructured },
                { name: 'محتوى حقيقي', value: analysis.hasRealContent }
            ];
            
            let html = '<div class="stats">';
            checks.forEach(check => {
                html += `
                    <div class="stat-card">
                        <div class="stat-number ${check.value ? 'excellent' : 'poor'}">${check.value ? '✅' : '❌'}</div>
                        <div>${check.name}</div>
                    </div>
                `;
            });
            html += '</div>';
            
            html += `
                <div class="info">
                    <strong>حجم المحتوى:</strong> ${analysis.totalSize.toLocaleString()} حرف<br>
                    <strong>الدوال المُطبقة:</strong> ${analysis.functionsDisplayed} من أصل 17 دالة متوقعة
                </div>
            `;
            
            analysisDiv.innerHTML = html;
        }
        
        function displayContentSample(sample, fullSize) {
            const sampleDiv = document.getElementById('contentSample');
            
            sampleDiv.innerHTML = `
                <div class="info">
                    <strong>حجم المحتوى الكامل:</strong> ${fullSize.toLocaleString()} حرف<br>
                    <strong>العينة المعروضة:</strong> أول 1000 حرف
                </div>
                <div class="code">${sample}...</div>
            `;
        }
        
        function displayTestDetails(result) {
            const detailsDiv = document.getElementById('testDetails');
            
            detailsDiv.innerHTML = `
                <div class="info">
                    <strong>وقت الاختبار:</strong> ${new Date(result.timestamp).toLocaleString('ar')}<br>
                    <strong>حالة الاختبار:</strong> ${result.success ? 'نجح' : 'فشل'}<br>
                    <strong>نوع الاختبار:</strong> العرض الشامل التفصيلي للدوال الـ36 والملفات الشاملة
                </div>
                
                <div class="result">
                    <strong>✅ تم اختبار:</strong><br>
                    • العرض الشامل التفصيلي الجديد<br>
                    • جميع المجموعات الخمس للدوال الـ36<br>
                    • الملفات الشاملة التفصيلية<br>
                    • ملخص حالة النظام v4.0<br>
                    • التنسيق والعرض المتناسق
                </div>
            `;
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة اختبار العرض الشامل جاهزة');
            runDisplayTest();
        });
    </script>
</body>
</html>
"@

# حفظ ملف HTML
$htmlFile = "comprehensive_display_test.html"
$testHTML | Out-File -FilePath $htmlFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف الاختبار: $htmlFile" -ForegroundColor Green

# فتح الاختبار في المتصفح
Write-Host "🌐 فتح الاختبار في المتصفح..." -ForegroundColor Yellow
Start-Process $htmlFile

# انتظار قليل ثم عرض الملخص
Write-Host "⏳ انتظار تشغيل الاختبار..." -ForegroundColor Cyan
Start-Sleep -Seconds 3

Write-Host "`n📊 ملخص اختبار العرض الشامل التفصيلي:" -ForegroundColor Yellow
Write-Host "✅ تم إنشاء اختبار شامل للعرض الجديد" -ForegroundColor Green
Write-Host "✅ يتم اختبار جميع المجموعات الخمس للدوال الـ36" -ForegroundColor Green
Write-Host "✅ يتم اختبار الملفات الشاملة التفصيلية" -ForegroundColor Green
Write-Host "✅ يتم فحص التنسيق والعرض المتناسق" -ForegroundColor Green

Write-Host "`n🔍 ما يتم فحصه في العرض الجديد:" -ForegroundColor Cyan
Write-Host "   📋 البنية الشاملة التفصيلية" -ForegroundColor White
Write-Host "   📂 المجموعات الخمس للدوال (1-5, 6-10, 11-20, 21-30, 31-36)" -ForegroundColor White
Write-Host "   📁 الملفات الشاملة (impact_visualizer.js, textual_impact_analyzer.js)" -ForegroundColor White
Write-Host "   📊 ملخص حالة النظام v4.0" -ForegroundColor White
Write-Host "   🎨 التنسيق والألوان والعرض المتناسق" -ForegroundColor White
Write-Host "   📝 المحتوى الحقيقي من الدوال (ليس رسائل عامة)" -ForegroundColor White

Write-Host "`n📋 النتائج متوفرة في:" -ForegroundColor Yellow
Write-Host "   🌐 المتصفح: $htmlFile" -ForegroundColor White
Write-Host "   📝 Console: F12 في المتصفح" -ForegroundColor White

Write-Host "`n✅ انتهى تشغيل اختبار العرض الشامل التفصيلي" -ForegroundColor Green
