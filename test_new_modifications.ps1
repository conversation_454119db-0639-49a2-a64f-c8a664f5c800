# Test New Modifications - Check specific changes made
Write-Host "Testing NEW MODIFICATIONS made to the system..." -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Test Results
$testResults = @{
    TextualAnalyzerAdded = $false
    RealContentDisplayFixed = 0
    EarlyReturnRemoved = $false
    CatchBlockFixed = $false
    ComprehensiveContentEnabled = 0
    TotalScore = 0
}

# 1. Check if textual_impact_analyzer.js was added to index.html
Write-Host "`n1. Checking if textual_impact_analyzer.js was added to index.html..." -ForegroundColor Yellow

$indexFile = "index.html"
if (Test-Path $indexFile) {
    $indexContent = Get-Content $indexFile -Raw -Encoding UTF8
    
    if ($indexContent -match "textual_impact_analyzer\.js") {
        Write-Host "   ✅ textual_impact_analyzer.js found in index.html" -ForegroundColor Green
        $testResults.TextualAnalyzerAdded = $true
    } else {
        Write-Host "   ❌ textual_impact_analyzer.js NOT found in index.html" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ index.html file not found" -ForegroundColor Red
}

# 2. Check for real content display modifications in BugBountyCore.js
Write-Host "`n2. Checking for real content display modifications..." -ForegroundColor Yellow

$coreFile = "assets\modules\bugbounty\BugBountyCore.js"
if (Test-Path $coreFile) {
    $coreContent = Get-Content $coreFile -Raw -Encoding UTF8
    
    # Check for specific modifications made
    $realContentModifications = @(
        "max-height: 200px; overflow-y: auto; background: #f8f9fa",
        "typeof vuln.comprehensive_details === 'object'",
        "JSON.stringify(vuln.comprehensive_details, null, 2)",
        "typeof vuln.dynamic_impact === 'object'",
        "typeof vuln.exploitation_steps === 'object'",
        "typeof vuln.visual_impact_data === 'object'",
        "typeof vuln.textual_impact_analysis === 'object'"
    )
    
    $foundModifications = 0
    foreach ($mod in $realContentModifications) {
        if ($coreContent -match [regex]::Escape($mod)) {
            $foundModifications++
            Write-Host "   ✅ Found modification: $mod" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Missing modification: $mod" -ForegroundColor Red
        }
    }
    
    $testResults.RealContentDisplayFixed = $foundModifications
    Write-Host "   📊 Real content modifications: $foundModifications/$($realContentModifications.Count)" -ForegroundColor White
    
} else {
    Write-Host "   ❌ BugBountyCore.js file not found" -ForegroundColor Red
}

# 3. Check if early return was removed
Write-Host "`n3. Checking if early return was removed..." -ForegroundColor Yellow

if (Test-Path $coreFile) {
    # Check for the problematic early return pattern
    $earlyReturnPattern = "return comprehensiveHTML;.*console\.log.*دخول generateVulnerabilitiesHTML"
    
    if ($coreContent -match $earlyReturnPattern) {
        Write-Host "   ❌ Early return still exists - blocking 36 functions" -ForegroundColor Red
        $testResults.EarlyReturnRemoved = $false
    } else {
        Write-Host "   ✅ Early return removed - 36 functions can execute" -ForegroundColor Green
        $testResults.EarlyReturnRemoved = $true
    }
}

# 4. Check if catch block was fixed
Write-Host "`n4. Checking if catch block was fixed..." -ForegroundColor Yellow

if (Test-Path $coreFile) {
    # Check if the simple fallback report was removed
    $simpleFallbackPattern = "throw error"

    if ($coreContent -match [regex]::Escape($simpleFallbackPattern)) {
        Write-Host "   ✅ Simple fallback report removed - errors will be visible" -ForegroundColor Green
        $testResults.CatchBlockFixed = $true
    } else {
        Write-Host "   ❌ Simple fallback report still exists" -ForegroundColor Red
        $testResults.CatchBlockFixed = $false
    }
}

# 5. Check for comprehensive content enablement
Write-Host "`n5. Checking for comprehensive content enablement..." -ForegroundColor Yellow

if (Test-Path $coreFile) {
    $comprehensivePatterns = @(
        "comprehensive.*template",
        "36.*function",
        "generateComprehensiveDetailsFromRealData.*await",
        "generateDynamicImpactForAnyVulnerability.*await",
        "generateRealExploitationStepsForVulnerabilityComprehensive.*await"
    )
    
    $foundPatterns = 0
    foreach ($pattern in $comprehensivePatterns) {
        if ($coreContent -match $pattern) {
            $foundPatterns++
            Write-Host "   ✅ Found pattern: $pattern" -ForegroundColor Green
        }
    }
    
    $testResults.ComprehensiveContentEnabled = $foundPatterns
    Write-Host "   📊 Comprehensive patterns: $foundPatterns/$($comprehensivePatterns.Count)" -ForegroundColor White
}

# 6. Test actual function execution flow
Write-Host "`n6. Testing function execution flow..." -ForegroundColor Yellow

if (Test-Path $coreFile) {
    # Check if the main function path is clear
    $functionFlowChecks = @(
        "generateVulnerabilitiesHTML.*async",
        "finalHTML.*=",
        "return finalHTML",
        "comprehensive_details.*await.*generateComprehensiveDetailsFromRealData"
    )
    
    $flowChecks = 0
    foreach ($check in $functionFlowChecks) {
        if ($coreContent -match $check) {
            $flowChecks++
            Write-Host "   ✅ Flow check passed: $check" -ForegroundColor Green
        }
    }
    
    Write-Host "   📊 Function flow checks: $flowChecks/$($functionFlowChecks.Count)" -ForegroundColor White
}

# 7. Calculate total score
Write-Host "`nCalculating total score..." -ForegroundColor Yellow

$maxModifications = 7
$maxPatterns = 5

$analyzerScore = if ($testResults.TextualAnalyzerAdded) { 15 } else { 0 }
$contentScore = [math]::Round(($testResults.RealContentDisplayFixed / $maxModifications) * 25, 2)
$returnScore = if ($testResults.EarlyReturnRemoved) { 20 } else { 0 }
$catchScore = if ($testResults.CatchBlockFixed) { 15 } else { 0 }
$comprehensiveScore = [math]::Round(($testResults.ComprehensiveContentEnabled / $maxPatterns) * 25, 2)

$testResults.TotalScore = $analyzerScore + $contentScore + $returnScore + $catchScore + $comprehensiveScore

# 8. Display final results
Write-Host "`nFINAL NEW MODIFICATIONS TEST RESULTS" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

Write-Host "Modification Status:" -ForegroundColor White
Write-Host "   textual_impact_analyzer.js Added: $($testResults.TextualAnalyzerAdded) ($analyzerScore/15 points)" -ForegroundColor White
Write-Host "   Real Content Display Fixed: $($testResults.RealContentDisplayFixed)/$maxModifications ($contentScore/25 points)" -ForegroundColor White
Write-Host "   Early Return Removed: $($testResults.EarlyReturnRemoved) ($returnScore/20 points)" -ForegroundColor White
Write-Host "   Catch Block Fixed: $($testResults.CatchBlockFixed) ($catchScore/15 points)" -ForegroundColor White
Write-Host "   Comprehensive Content Enabled: $($testResults.ComprehensiveContentEnabled)/$maxPatterns ($comprehensiveScore/25 points)" -ForegroundColor White

Write-Host "`nTotal Score: $([math]::Round($testResults.TotalScore, 2))/100" -ForegroundColor Yellow

if ($testResults.TotalScore -ge 90) {
    Write-Host "EXCELLENT! All new modifications applied successfully!" -ForegroundColor Green
} elseif ($testResults.TotalScore -ge 70) {
    Write-Host "GOOD! Most new modifications applied" -ForegroundColor Yellow
} elseif ($testResults.TotalScore -ge 50) {
    Write-Host "PARTIAL! Some new modifications applied" -ForegroundColor Yellow
} else {
    Write-Host "NEEDS WORK! New modifications not applied properly" -ForegroundColor Red
}

# 9. Provide specific status
Write-Host "`nSpecific Status:" -ForegroundColor Cyan

if ($testResults.TextualAnalyzerAdded) {
    Write-Host "   ✅ textual_impact_analyzer.js is now loaded in index.html" -ForegroundColor Green
} else {
    Write-Host "   ❌ textual_impact_analyzer.js still missing from index.html" -ForegroundColor Red
}

if ($testResults.RealContentDisplayFixed -ge 5) {
    Write-Host "   ✅ Real content display modifications mostly applied" -ForegroundColor Green
} else {
    Write-Host "   ❌ Real content display modifications need more work" -ForegroundColor Red
}

if ($testResults.EarlyReturnRemoved) {
    Write-Host "   ✅ Early return removed - 36 functions can now execute" -ForegroundColor Green
} else {
    Write-Host "   ❌ Early return still blocking 36 functions execution" -ForegroundColor Red
}

if ($testResults.CatchBlockFixed) {
    Write-Host "   ✅ Catch block fixed - real errors will be visible" -ForegroundColor Green
} else {
    Write-Host "   ❌ Catch block still hiding real errors" -ForegroundColor Red
}

if ($testResults.ComprehensiveContentEnabled -ge 3) {
    Write-Host "   ✅ Comprehensive content system mostly enabled" -ForegroundColor Green
} else {
    Write-Host "   ❌ Comprehensive content system needs more enablement" -ForegroundColor Red
}

# 10. Next steps recommendation
Write-Host "`nNext Steps:" -ForegroundColor Cyan

if ($testResults.TotalScore -ge 90) {
    Write-Host "   🎉 System ready for testing! Try running a Bug Bounty scan now." -ForegroundColor Green
} elseif ($testResults.TotalScore -ge 70) {
    Write-Host "   🔧 Minor fixes needed. Address remaining issues and test again." -ForegroundColor Yellow
} else {
    Write-Host "   🚨 Major fixes needed. Review and apply missing modifications." -ForegroundColor Red
}

# Save detailed results
$reportContent = "# New Modifications Test Results`n"
$reportContent += "Test Date: $timestamp`n`n"
$reportContent += "## Modifications Applied:`n"
$reportContent += "textual_impact_analyzer.js Added: $($testResults.TextualAnalyzerAdded)`n"
$reportContent += "Real Content Display Fixed: $($testResults.RealContentDisplayFixed)/$maxModifications`n"
$reportContent += "Early Return Removed: $($testResults.EarlyReturnRemoved)`n"
$reportContent += "Catch Block Fixed: $($testResults.CatchBlockFixed)`n"
$reportContent += "Comprehensive Content Enabled: $($testResults.ComprehensiveContentEnabled)/$maxPatterns`n`n"
$reportContent += "## Total Score: $([math]::Round($testResults.TotalScore, 2))/100`n`n"

if ($testResults.TotalScore -ge 90) {
    $assessment = "EXCELLENT! All new modifications applied successfully!"
} elseif ($testResults.TotalScore -ge 70) {
    $assessment = "GOOD! Most new modifications applied"
} elseif ($testResults.TotalScore -ge 50) {
    $assessment = "PARTIAL! Some new modifications applied"
} else {
    $assessment = "NEEDS WORK! New modifications not applied properly"
}

$reportContent += "## Assessment: $assessment`n`n"
$reportContent += "## Key Status:`n"
$reportContent += "File Loading: $($testResults.TextualAnalyzerAdded)`n"
$reportContent += "Content Display: $($testResults.RealContentDisplayFixed -ge 5)`n"
$reportContent += "Function Execution: $($testResults.EarlyReturnRemoved)`n"
$reportContent += "Error Handling: $($testResults.CatchBlockFixed)`n"
$reportContent += "System Integration: $($testResults.ComprehensiveContentEnabled -ge 3)`n"

$reportContent | Out-File -FilePath "new_modifications_test_report.md" -Encoding UTF8
Write-Host "`nDetailed test report saved to: new_modifications_test_report.md" -ForegroundColor Green

Write-Host "`nNew modifications test completed" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
