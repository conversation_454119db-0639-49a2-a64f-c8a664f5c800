# اختبار شامل للتقارير (الرئيسي والمنفصل) من خلال PowerShell
# للتحقق من عرض المحتوى بشكل صحيح ومتناسق وليس متداخل

Write-Host "🔥 بدء اختبار التقارير الشامل للدوال الـ36..." -ForegroundColor Yellow

# التحقق من وجود الملفات المطلوبة
$requiredFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "test_reports_comprehensive.js"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ الملف المطلوب غير موجود: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# إنشاء ملف HTML للاختبار
$testHTML = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير الشامل</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار التقارير الشامل للدوال الـ36</h1>
        
        <div class="section">
            <h2>🧪 حالة الاختبار</h2>
            <div id="testStatus">⏳ جاري تحميل النظام...</div>
        </div>
        
        <div class="section">
            <h2>📊 إحصائيات التقارير</h2>
            <div id="reportStats"></div>
        </div>
        
        <div class="section">
            <h2>📋 تحليل التقرير الرئيسي</h2>
            <div id="mainReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>📄 تحليل التقرير المنفصل</h2>
            <div id="separateReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>🔍 مقارنة التقارير</h2>
            <div id="reportsComparison"></div>
        </div>
        
        <div class="section">
            <h2>💡 التوصيات</h2>
            <div id="recommendations"></div>
        </div>
        
        <div class="section">
            <h2>📝 عينات من المحتوى</h2>
            <div id="contentSamples"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_reports_comprehensive.js"></script>
    
    <script>
        async function runComprehensiveTest() {
            const statusDiv = document.getElementById('testStatus');
            
            try {
                statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل الاختبار الشامل...</div>';
                
                // تشغيل الاختبار
                const result = await testComprehensiveReports();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من الاختبار بنجاح!</div>';
                    
                    // عرض الإحصائيات
                    displayReportStats(result);
                    
                    // عرض تحليل التقارير
                    displayReportAnalysis(result.mainReport.analysis, 'mainReportAnalysis');
                    displayReportAnalysis(result.separateReport.analysis, 'separateReportAnalysis');
                    
                    // عرض المقارنة
                    displayComparison(result.comparison);
                    
                    // عرض التوصيات
                    displayRecommendations(result.comparison.recommendation);
                    
                    // عرض عينات المحتوى
                    displayContentSamples(result);
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ فشل الاختبار: ' + result.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        function displayReportStats(result) {
            const statsDiv = document.getElementById('reportStats');
            statsDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${result.vulnerabilities_processed}</div>
                        <div>ثغرات معالجة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.functions_applied}</div>
                        <div>دالة مطبقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.mainReport.size / 1024)} KB</div>
                        <div>حجم التقرير الرئيسي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.separateReport.size / 1024)} KB</div>
                        <div>حجم التقرير المنفصل</div>
                    </div>
                </div>
            `;
        }
        
        function displayReportAnalysis(analysis, elementId) {
            const element = document.getElementById(elementId);
            const qualityClass = analysis.contentQuality;
            
            let html = `
                <div class="info">
                    <strong>نوع التقرير:</strong> ${analysis.type}<br>
                    <strong>حجم المحتوى:</strong> ${analysis.totalSize.toLocaleString()} حرف<br>
                    <strong>جودة المحتوى:</strong> <span class="${qualityClass}">${getQualityText(analysis.contentQuality)}</span>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveContent ? 'excellent' : 'poor'}">${analysis.hasComprehensiveContent ? '✅' : '❌'}</div>
                        <div>محتوى شامل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasRealData ? 'excellent' : 'poor'}">${analysis.hasRealData ? '✅' : '❌'}</div>
                        <div>بيانات حقيقية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.isWellFormatted ? 'excellent' : 'poor'}">${analysis.isWellFormatted ? '✅' : '❌'}</div>
                        <div>تنسيق جيد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${!analysis.hasGenericMessages ? 'excellent' : 'poor'}">${!analysis.hasGenericMessages ? '✅' : '❌'}</div>
                        <div>بدون رسائل عامة</div>
                    </div>
                </div>
            `;
            
            if (analysis.issues.length > 0) {
                html += '<div class="warning"><strong>المشاكل المكتشفة:</strong><ul>';
                analysis.issues.forEach(issue => {
                    html += `<li>${issue}</li>`;
                });
                html += '</ul></div>';
            }
            
            element.innerHTML = html;
        }
        
        function displayComparison(comparison) {
            const element = document.getElementById('reportsComparison');
            
            element.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${comparison.qualityMatch ? 'excellent' : 'warning'}">${comparison.qualityMatch ? '✅' : '⚠️'}</div>
                        <div>جودة متطابقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${comparison.bothHaveComprehensiveContent ? 'excellent' : 'poor'}">${comparison.bothHaveComprehensiveContent ? '✅' : '❌'}</div>
                        <div>محتوى شامل في كليهما</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${comparison.bothHaveRealData ? 'excellent' : 'poor'}">${comparison.bothHaveRealData ? '✅' : '❌'}</div>
                        <div>بيانات حقيقية في كليهما</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(comparison.sizeDifference / 1024)} KB</div>
                        <div>فرق الحجم</div>
                    </div>
                </div>
                
                <div class="info">
                    <strong>مشاكل التقرير الرئيسي:</strong> ${comparison.issuesComparison.main}<br>
                    <strong>مشاكل التقرير المنفصل:</strong> ${comparison.issuesComparison.separate}
                </div>
            `;
        }
        
        function displayRecommendations(recommendations) {
            const element = document.getElementById('recommendations');
            
            let html = '<ul>';
            recommendations.forEach(rec => {
                html += `<li>${rec}</li>`;
            });
            html += '</ul>';
            
            element.innerHTML = html;
        }
        
        function displayContentSamples(result) {
            const element = document.getElementById('contentSamples');
            
            element.innerHTML = `
                <h4>📋 عينة من التقرير الرئيسي:</h4>
                <div class="code">${result.mainReport.sample}</div>
                
                <h4>📄 عينة من التقرير المنفصل:</h4>
                <div class="code">${result.separateReport.sample}</div>
            `;
        }
        
        function getQualityText(quality) {
            const qualityTexts = {
                'excellent': 'ممتاز',
                'good': 'جيد',
                'fair': 'مقبول',
                'poor': 'ضعيف'
            };
            return qualityTexts[quality] || quality;
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة الاختبار جاهزة');
            runComprehensiveTest();
        });
    </script>
</body>
</html>
"@

# حفظ ملف HTML
$htmlFile = "comprehensive_test_results.html"
$testHTML | Out-File -FilePath $htmlFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف الاختبار: $htmlFile" -ForegroundColor Green

# فتح الاختبار في المتصفح
Write-Host "🌐 فتح الاختبار في المتصفح..." -ForegroundColor Yellow
Start-Process $htmlFile

# انتظار قليل ثم قراءة النتائج من console
Write-Host "⏳ انتظار تشغيل الاختبار..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

Write-Host "`n📊 ملخص الاختبار:" -ForegroundColor Yellow
Write-Host "✅ تم إنشاء اختبار شامل للتقارير" -ForegroundColor Green
Write-Host "✅ يتم اختبار التقرير الرئيسي والمنفصل" -ForegroundColor Green
Write-Host "✅ يتم فحص الدوال الـ36 والملفات الشاملة" -ForegroundColor Green
Write-Host "✅ يتم التحقق من عدم التداخل والتنسيق" -ForegroundColor Green

Write-Host "`n🔍 ما يتم فحصه:" -ForegroundColor Cyan
Write-Host "   📋 المحتوى الشامل من الدوال الـ36" -ForegroundColor White
Write-Host "   📊 البيانات الحقيقية المستخرجة من الثغرات" -ForegroundColor White
Write-Host "   🎨 التنسيق والعرض المتناسق" -ForegroundColor White
Write-Host "   ⚠️ الرسائل العامة والمحتوى المكرر" -ForegroundColor White
Write-Host "   🔗 التداخل في عناصر HTML" -ForegroundColor White

Write-Host "`n📋 النتائج متوفرة في:" -ForegroundColor Yellow
Write-Host "   🌐 المتصفح: $htmlFile" -ForegroundColor White
Write-Host "   📝 Console: F12 في المتصفح" -ForegroundColor White

Write-Host "`n✅ انتهى تشغيل اختبار التقارير الشامل" -ForegroundColor Green
