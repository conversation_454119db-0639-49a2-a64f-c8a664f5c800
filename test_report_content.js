// Test Report Content - Check if exported reports contain REAL comprehensive content
console.log('🔥 بدء اختبار المحتوى الحقيقي للتقارير المُصدرة...');

// Load required modules
const fs = require('fs');
const path = require('path');

// Test results
const testResults = {
    mainReportGenerated: false,
    separateReportGenerated: false,
    comprehensiveContentFound: 0,
    realDataDisplayFound: 0,
    functions36ContentFound: 0,
    filesContentFound: 0,
    arabicContentFound: 0,
    totalScore: 0
};

// Function to test report content
async function testReportContent() {
    try {
        console.log('\n📊 إنشاء تقرير اختبار لفحص المحتوى الحقيقي...');
        
        // Simulate loading BugBountyCore
        const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
        const bugBountyCore = new BugBountyCore();
        
        // Create test vulnerability
        const testVuln = {
            name: 'API Authentication Bypass',
            type: 'Authentication Bypass',
            severity: 'Medium',
            url: 'http://testphp.vulnweb.com',
            parameter: 'auth_token',
            description: 'ثغرة تجاوز المصادقة في API',
            payload: 'admin\'||1=1--',
            evidence: 'استجابة الخادم تشير إلى نجاح تجاوز المصادقة'
        };
        
        console.log('🎯 تطبيق الدوال الـ36 الشاملة التفصيلية...');
        
        // Extract real data
        const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability ? 
            bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln) : {};
        
        console.log('📋 البيانات الحقيقية المستخرجة:', realData);
        
        // Apply comprehensive functions
        if (bugBountyCore.generateComprehensiveDetailsFromRealData) {
            testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
            console.log('✅ comprehensive_details:', testVuln.comprehensive_details);
        }
        
        if (bugBountyCore.generateDynamicImpactForAnyVulnerability) {
            testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
            console.log('✅ dynamic_impact:', testVuln.dynamic_impact);
        }
        
        if (bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive) {
            testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
            console.log('✅ exploitation_steps:', testVuln.exploitation_steps);
        }
        
        if (bugBountyCore.generateDynamicRecommendationsForVulnerability) {
            testVuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln, realData);
            console.log('✅ dynamic_recommendations:', testVuln.dynamic_recommendations);
        }
        
        // Apply system files
        console.log('📁 تطبيق ملفات النظام...');
        
        try {
            const ImpactVisualizer = require('./assets/modules/bugbounty/impact_visualizer.js');
            const impactVisualizer = new ImpactVisualizer(bugBountyCore);
            testVuln.visual_impact_data = await impactVisualizer.createVulnerabilityVisualization(testVuln, realData);
            console.log('✅ visual_impact_data:', testVuln.visual_impact_data);
        } catch (error) {
            console.log('❌ خطأ في impact_visualizer.js:', error.message);
        }
        
        try {
            const TextualImpactAnalyzer = require('./assets/modules/bugbounty/textual_impact_analyzer.js');
            const textualAnalyzer = new TextualImpactAnalyzer();
            testVuln.textual_impact_analysis = await textualAnalyzer.analyzeVulnerabilityImpact(testVuln, realData);
            console.log('✅ textual_impact_analysis:', testVuln.textual_impact_analysis);
        } catch (error) {
            console.log('❌ خطأ في textual_impact_analyzer.js:', error.message);
        }
        
        // Generate main report
        console.log('\n📄 إنشاء التقرير الرئيسي...');
        const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
        console.log('📊 حجم التقرير الرئيسي:', mainReport ? mainReport.length : 0, 'حرف');
        
        // Generate separate report
        console.log('\n📄 إنشاء التقرير المنفصل...');
        const pageData = {
            page_name: 'صفحة الاختبار',
            page_url: 'http://testphp.vulnweb.com',
            vulnerabilities: [testVuln]
        };
        const separateReport = await bugBountyCore.formatSinglePageReport(pageData);
        console.log('📊 حجم التقرير المنفصل:', separateReport ? separateReport.length : 0, 'حرف');
        
        // Analyze content
        console.log('\n🔍 تحليل محتوى التقارير...');
        
        if (mainReport) {
            testResults.mainReportGenerated = true;
            analyzeReportContent(mainReport, 'التقرير الرئيسي');
        }
        
        if (separateReport) {
            testResults.separateReportGenerated = true;
            analyzeReportContent(separateReport, 'التقرير المنفصل');
        }
        
        // Calculate score
        calculateFinalScore();
        
        // Display results
        displayResults();
        
        // Save test results
        saveTestResults(mainReport, separateReport);
        
        return {
            success: true,
            mainReportSize: mainReport ? mainReport.length : 0,
            separateReportSize: separateReport ? separateReport.length : 0,
            testResults: testResults
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار المحتوى:', error);
        return { success: false, error: error.message };
    }
}

// Function to analyze report content
function analyzeReportContent(reportHTML, reportType) {
    console.log(`\n🔍 تحليل ${reportType}...`);
    
    // Check for comprehensive content indicators
    const comprehensiveIndicators = [
        'تحليل شامل تفصيلي',
        'تفاصيل الاكتشاف الحقيقية',
        'نتائج الاختبار الحقيقية',
        'التحليل التقني المفصل',
        'تقييم المخاطر',
        'التوصيات الأمنية',
        'comprehensive_description',
        'technical_details'
    ];
    
    let foundComprehensive = 0;
    comprehensiveIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            foundComprehensive++;
            console.log(`   ✅ وُجد محتوى شامل: ${indicator}`);
        }
    });
    
    // Check for real data display
    const realDataIndicators = [
        'max-height: 200px; overflow-y: auto',
        'JSON.stringify',
        'typeof.*object',
        'comprehensive_details',
        'dynamic_impact',
        'exploitation_steps'
    ];
    
    let foundRealData = 0;
    realDataIndicators.forEach(indicator => {
        if (reportHTML.match(indicator)) {
            foundRealData++;
            console.log(`   ✅ وُجد عرض بيانات حقيقية: ${indicator}`);
        }
    });
    
    // Check for 36 functions content
    const functionsIndicators = [
        'generateComprehensiveDetailsFromRealData',
        'generateDynamicImpactForAnyVulnerability',
        'generateRealExploitationStepsForVulnerabilityComprehensive',
        'generateDynamicRecommendationsForVulnerability',
        'extractRealDataFromDiscoveredVulnerability'
    ];
    
    let foundFunctions = 0;
    functionsIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            foundFunctions++;
            console.log(`   ✅ وُجد محتوى الدوال: ${indicator}`);
        }
    });
    
    // Check for system files content
    const filesIndicators = [
        'impact_visualizer.js',
        'textual_impact_analyzer.js',
        'visual_impact_data',
        'textual_impact_analysis'
    ];
    
    let foundFiles = 0;
    filesIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            foundFiles++;
            console.log(`   ✅ وُجد محتوى الملفات: ${indicator}`);
        }
    });
    
    // Check for Arabic content
    const arabicIndicators = [
        'خطوات الاستغلال',
        'تحليل التأثير',
        'المخاطر الأمنية',
        'الحلول المقترحة',
        'التدابير الوقائية'
    ];
    
    let foundArabic = 0;
    arabicIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            foundArabic++;
            console.log(`   ✅ وُجد محتوى عربي: ${indicator}`);
        }
    });
    
    // Update test results
    testResults.comprehensiveContentFound += foundComprehensive;
    testResults.realDataDisplayFound += foundRealData;
    testResults.functions36ContentFound += foundFunctions;
    testResults.filesContentFound += foundFiles;
    testResults.arabicContentFound += foundArabic;
    
    console.log(`📊 ملخص ${reportType}:`);
    console.log(`   المحتوى الشامل: ${foundComprehensive}/${comprehensiveIndicators.length}`);
    console.log(`   عرض البيانات الحقيقية: ${foundRealData}/${realDataIndicators.length}`);
    console.log(`   محتوى الدوال الـ36: ${foundFunctions}/${functionsIndicators.length}`);
    console.log(`   محتوى الملفات: ${foundFiles}/${filesIndicators.length}`);
    console.log(`   المحتوى العربي: ${foundArabic}/${arabicIndicators.length}`);
}

// Function to calculate final score
function calculateFinalScore() {
    const maxComprehensive = 16; // 8 indicators * 2 reports
    const maxRealData = 12;      // 6 indicators * 2 reports
    const maxFunctions = 10;     // 5 indicators * 2 reports
    const maxFiles = 8;          // 4 indicators * 2 reports
    const maxArabic = 10;        // 5 indicators * 2 reports
    
    const comprehensiveScore = Math.min((testResults.comprehensiveContentFound / maxComprehensive) * 30, 30);
    const realDataScore = Math.min((testResults.realDataDisplayFound / maxRealData) * 25, 25);
    const functionsScore = Math.min((testResults.functions36ContentFound / maxFunctions) * 20, 20);
    const filesScore = Math.min((testResults.filesContentFound / maxFiles) * 15, 15);
    const arabicScore = Math.min((testResults.arabicContentFound / maxArabic) * 10, 10);
    
    testResults.totalScore = comprehensiveScore + realDataScore + functionsScore + filesScore + arabicScore;
}

// Function to display results
function displayResults() {
    console.log('\n🏆 النتائج النهائية لاختبار محتوى التقارير');
    console.log('===========================================');
    
    console.log('📊 حالة التقارير:');
    console.log(`   التقرير الرئيسي: ${testResults.mainReportGenerated ? '✅ تم إنشاؤه' : '❌ لم يتم إنشاؤه'}`);
    console.log(`   التقرير المنفصل: ${testResults.separateReportGenerated ? '✅ تم إنشاؤه' : '❌ لم يتم إنشاؤه'}`);
    
    console.log('\n📋 تحليل المحتوى:');
    console.log(`   المحتوى الشامل: ${testResults.comprehensiveContentFound}/16`);
    console.log(`   عرض البيانات الحقيقية: ${testResults.realDataDisplayFound}/12`);
    console.log(`   محتوى الدوال الـ36: ${testResults.functions36ContentFound}/10`);
    console.log(`   محتوى الملفات: ${testResults.filesContentFound}/8`);
    console.log(`   المحتوى العربي: ${testResults.arabicContentFound}/10`);
    
    console.log(`\n🎯 النتيجة الإجمالية: ${Math.round(testResults.totalScore)}/100`);
    
    if (testResults.totalScore >= 90) {
        console.log('🎉 ممتاز! التقارير تحتوي على محتوى شامل تفصيلي حقيقي!');
        console.log('✅ الدوال الـ36 والملفات تعمل بشكل مثالي في التقارير المُصدرة!');
    } else if (testResults.totalScore >= 70) {
        console.log('👍 جيد! معظم المحتوى الشامل موجود في التقارير!');
        console.log('🔧 بعض التحسينات مطلوبة للحصول على محتوى شامل كامل');
    } else if (testResults.totalScore >= 50) {
        console.log('⚠️ جزئي! بعض المحتوى الشامل موجود!');
        console.log('🚨 تحسينات كبيرة مطلوبة للمحتوى الشامل');
    } else {
        console.log('❌ يحتاج عمل! التقارير تفتقر للمحتوى الشامل التفصيلي!');
        console.log('🚨 الدوال الـ36 والملفات لا تعمل بشكل صحيح في التقارير المُصدرة');
    }
}

// Function to save test results
function saveTestResults(mainReport, separateReport) {
    const results = {
        timestamp: new Date().toISOString(),
        testResults: testResults,
        mainReportSize: mainReport ? mainReport.length : 0,
        separateReportSize: separateReport ? separateReport.length : 0,
        mainReportSample: mainReport ? mainReport.substring(0, 1000) : null,
        separateReportSample: separateReport ? separateReport.substring(0, 1000) : null
    };
    
    try {
        fs.writeFileSync('test_results.json', JSON.stringify(results, null, 2), 'utf8');
        console.log('\n💾 تم حفظ نتائج الاختبار في test_results.json');
    } catch (error) {
        console.error('❌ خطأ في حفظ النتائج:', error.message);
    }
}

// Run the test
if (require.main === module) {
    testReportContent().then(result => {
        console.log('\n✅ اكتمل اختبار محتوى التقارير');
        process.exit(result.success ? 0 : 1);
    }).catch(error => {
        console.error('❌ فشل اختبار محتوى التقارير:', error);
        process.exit(1);
    });
}

module.exports = { testReportContent, analyzeReportContent };
