<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الدوال الـ36</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة الدوال الـ36 الشاملة التفصيلية</h1>
        
        <div class="section">
            <h2>🧪 اختبار مباشر للدوال</h2>
            <button onclick="runDirectTest()">🚀 اختبار مباشر للدوال</button>
            <button onclick="runFullSystemTest()">🔥 اختبار النظام الكامل</button>
            <button onclick="clearResults()">🧹 مسح النتائج</button>
            <div id="testStatus"></div>
        </div>
        
        <div class="section">
            <h2>📊 نتائج الاختبار المباشر</h2>
            <div id="directResults"></div>
        </div>
        
        <div class="section">
            <h2>🔥 نتائج اختبار النظام الكامل</h2>
            <div id="systemResults"></div>
        </div>
        
        <div class="section">
            <h2>🔍 تحليل المشكلة</h2>
            <div id="problemAnalysis"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    
    <script>
        let bugBountyCore = null;
        
        async function initializeBugBounty() {
            try {
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore غير محمل');
                }
                
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة BugBountyCore');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة BugBountyCore:', error);
                return false;
            }
        }
        
        async function runDirectTest() {
            const statusDiv = document.getElementById('testStatus');
            const resultsDiv = document.getElementById('directResults');
            
            statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل الاختبار المباشر...</div>';
            resultsDiv.innerHTML = '';
            
            try {
                // تهيئة النظام
                const initialized = await initializeBugBounty();
                if (!initialized) {
                    throw new Error('فشل في تهيئة النظام');
                }
                
                // ثغرة تجريبية
                const testVuln = {
                    name: 'SQL Injection في نموذج تسجيل الدخول',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://example.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1' --",
                    location: 'Login Form'
                };
                
                const testRealData = {
                    payload_used: "admin' OR '1'='1' --",
                    response_received: 'MySQL Error: You have an error in your SQL syntax',
                    impact_observed: 'تم تجاوز المصادقة بنجاح',
                    evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL',
                    injection_point: 'username parameter في POST request',
                    exploitation_result: 'تم الدخول كمدير بدون كلمة مرور'
                };
                
                let results = '<h3>📊 نتائج الاختبار المباشر:</h3>';
                
                // اختبار الدالة الأولى
                console.log('🔍 اختبار generateComprehensiveDetailsFromRealData...');
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                
                results += '<h4>🔍 generateComprehensiveDetailsFromRealData:</h4>';
                results += '<div class="result">النوع: ' + typeof comprehensiveDetails + '</div>';
                results += '<div class="result">الطول: ' + (comprehensiveDetails ? comprehensiveDetails.length : 'undefined') + ' حرف</div>';
                
                if (comprehensiveDetails && comprehensiveDetails.length > 50) {
                    results += '<div class="result">✅ الدالة تعمل بشكل صحيح</div>';
                    results += '<div class="code">' + comprehensiveDetails.substring(0, 500) + '...</div>';
                } else {
                    results += '<div class="error">❌ الدالة لا تُنتج محتوى كافي</div>';
                    results += '<div class="code">' + (comprehensiveDetails || 'null') + '</div>';
                }
                
                // اختبار الدالة الثانية
                console.log('🔍 اختبار generateComprehensiveVulnerabilityAnalysis...');
                const comprehensiveAnalysis = await bugBountyCore.generateComprehensiveVulnerabilityAnalysis(testVuln, testRealData);
                
                results += '<h4>🔬 generateComprehensiveVulnerabilityAnalysis:</h4>';
                results += '<div class="result">النوع: ' + typeof comprehensiveAnalysis + '</div>';
                results += '<div class="result">الطول: ' + (comprehensiveAnalysis ? comprehensiveAnalysis.length : 'undefined') + ' حرف</div>';
                
                if (comprehensiveAnalysis && comprehensiveAnalysis.length > 50) {
                    results += '<div class="result">✅ الدالة تعمل بشكل صحيح</div>';
                    results += '<div class="code">' + comprehensiveAnalysis.substring(0, 500) + '...</div>';
                } else {
                    results += '<div class="error">❌ الدالة لا تُنتج محتوى كافي</div>';
                    results += '<div class="code">' + (comprehensiveAnalysis || 'null') + '</div>';
                }
                
                // اختبار الدالة الثالثة
                console.log('🔍 اختبار generateDynamicSecurityImpactAnalysis...');
                const securityImpact = await bugBountyCore.generateDynamicSecurityImpactAnalysis(testVuln, testRealData);
                
                results += '<h4>🛡️ generateDynamicSecurityImpactAnalysis:</h4>';
                results += '<div class="result">النوع: ' + typeof securityImpact + '</div>';
                results += '<div class="result">الطول: ' + (securityImpact ? securityImpact.length : 'undefined') + ' حرف</div>';
                
                if (securityImpact && securityImpact.length > 50) {
                    results += '<div class="result">✅ الدالة تعمل بشكل صحيح</div>';
                    results += '<div class="code">' + securityImpact.substring(0, 500) + '...</div>';
                } else {
                    results += '<div class="error">❌ الدالة لا تُنتج محتوى كافي</div>';
                    results += '<div class="code">' + (securityImpact || 'null') + '</div>';
                }
                
                statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من الاختبار المباشر</div>';
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        async function runFullSystemTest() {
            const statusDiv = document.getElementById('testStatus');
            const resultsDiv = document.getElementById('systemResults');
            
            statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل اختبار النظام الكامل...</div>';
            resultsDiv.innerHTML = '';
            
            try {
                // تهيئة النظام
                const initialized = await initializeBugBounty();
                if (!initialized) {
                    throw new Error('فشل في تهيئة النظام');
                }
                
                // ثغرة تجريبية
                const testVuln = {
                    name: 'XSS في حقل البحث',
                    type: 'Cross-Site Scripting',
                    severity: 'High',
                    url: 'https://example.com/search.php',
                    parameter: 'query',
                    payload: '<script>alert("XSS")</script>',
                    location: 'Search Form'
                };
                
                const testRealData = {
                    payload_used: '<script>alert("XSS")</script>',
                    response_received: 'تم عرض الكود في الصفحة بدون تنظيف',
                    impact_observed: 'تنفيذ JavaScript في المتصفح',
                    evidence_found: 'ظهور نافذة alert في المتصفح',
                    injection_point: 'query parameter في GET request',
                    exploitation_result: 'تم تنفيذ كود JavaScript بنجاح'
                };
                
                // تطبيق الدوال على الثغرة (محاكاة النظام الحقيقي)
                console.log('🔥 تطبيق الدوال على الثغرة...');
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                testVuln.comprehensive_analysis = await bugBountyCore.generateComprehensiveVulnerabilityAnalysis(testVuln, testRealData);
                testVuln.security_impact_analysis = await bugBountyCore.generateDynamicSecurityImpactAnalysis(testVuln, testRealData);
                
                // محاكاة العرض في HTML (كما يحدث في النظام الحقيقي)
                let htmlOutput = '<h3>🔥 محاكاة العرض في التقرير:</h3>';
                
                htmlOutput += '<h4>✅ generateComprehensiveDetailsFromRealData:</h4>';
                const display1 = testVuln.comprehensive_details && typeof testVuln.comprehensive_details === 'string' && testVuln.comprehensive_details.length > 50 ? 
                    testVuln.comprehensive_details : 
                    testVuln.comprehensive_details && typeof testVuln.comprehensive_details === 'object' ? 
                        JSON.stringify(testVuln.comprehensive_details, null, 2) : 
                        '⚠️ لم يتم إنتاج محتوى شامل - يجب فحص الدالة';
                htmlOutput += '<div class="code">' + display1.substring(0, 300) + '...</div>';
                
                htmlOutput += '<h4>✅ generateComprehensiveVulnerabilityAnalysis:</h4>';
                const display2 = testVuln.comprehensive_analysis || '⚠️ لم يتم إنتاج تحليل شامل';
                htmlOutput += '<div class="code">' + display2.substring(0, 300) + '...</div>';
                
                htmlOutput += '<h4>✅ generateDynamicSecurityImpactAnalysis:</h4>';
                const display3 = testVuln.security_impact_analysis || '⚠️ لم يتم إنتاج تحليل أمني';
                htmlOutput += '<div class="code">' + display3.substring(0, 300) + '...</div>';
                
                statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار النظام الكامل</div>';
                resultsDiv.innerHTML = htmlOutput;
                
                // تحليل المشكلة
                let analysis = '<h3>🔍 تحليل المشكلة:</h3>';
                
                if (testVuln.comprehensive_details && testVuln.comprehensive_details.length > 50) {
                    analysis += '<div class="result">✅ الدوال تعمل بشكل صحيح وتُنتج محتوى شامل</div>';
                    analysis += '<div class="result">✅ المشكلة ليست في الدوال نفسها</div>';
                    analysis += '<div class="warning">⚠️ المشكلة قد تكون في:</div>';
                    analysis += '<div class="warning">1. توقيت تطبيق الدوال (async/await)</div>';
                    analysis += '<div class="warning">2. حفظ النتائج في كائن الثغرة</div>';
                    analysis += '<div class="warning">3. استخراج البيانات الحقيقية</div>';
                    analysis += '<div class="warning">4. عرض النتائج في القوالب</div>';
                } else {
                    analysis += '<div class="error">❌ الدوال لا تُنتج محتوى شامل كافي</div>';
                    analysis += '<div class="error">🔧 يجب إصلاح الدوال نفسها</div>';
                }
                
                document.getElementById('problemAnalysis').innerHTML = analysis;
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في اختبار النظام: ' + error.message + '</div>';
                console.error('خطأ في اختبار النظام:', error);
            }
        }
        
        function clearResults() {
            document.getElementById('testStatus').innerHTML = '';
            document.getElementById('directResults').innerHTML = '';
            document.getElementById('systemResults').innerHTML = '';
            document.getElementById('problemAnalysis').innerHTML = '';
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة التشخيص جاهزة');
        });
    </script>
</body>
</html>
