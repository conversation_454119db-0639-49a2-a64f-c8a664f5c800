/**
 * اختبار شامل للتقارير المُصدرة (الرئيسي والمنفصل)
 * للتحقق من عرض المحتوى بشكل صحيح ومتناسق وليس متداخل
 * مع استخدام الدوال الـ36 والملفات الشاملة التفصيلية ديناميكياً
 */

console.log('🔥 بدء اختبار التقارير المُصدرة الشامل...');

// بيانات ثغرات شاملة للاختبار
const comprehensiveTestVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        location: 'Login Form',
        method: 'POST'
    },
    {
        name: 'Cross-Site Scripting في حقل البحث',
        type: 'XSS',
        severity: 'High',
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>',
        location: 'Search Form',
        method: 'GET'
    },
    {
        name: 'CSRF في تغيير كلمة المرور',
        type: 'CSRF',
        severity: 'Medium',
        url: 'https://example.com/change-password.php',
        parameter: 'new_password',
        payload: '<form action="..." method="post">',
        location: 'Password Change Form',
        method: 'POST'
    }
];

// بيانات حقيقية شاملة لكل نوع ثغرة
const comprehensiveRealDataByType = {
    'SQL Injection': {
        payload_used: "admin' OR '1'='1' --",
        response_received: 'MySQL Error: You have an error in your SQL syntax near OR at line 1',
        impact_observed: 'تم تجاوز المصادقة بنجاح والوصول لحساب المدير',
        evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL والجداول',
        injection_point: 'username parameter في POST request',
        exploitation_result: 'تم الدخول كمدير بدون كلمة مرور صحيحة',
        database_info: 'MySQL 5.7.33, database: webapp_db, table: users',
        extracted_data: 'usernames, password hashes, email addresses',
        technical_details: 'Boolean-based blind SQL injection vulnerability',
        business_impact: 'Complete compromise of user authentication system',
        security_implications: 'Unauthorized access to all user accounts and sensitive data'
    },
    'XSS': {
        payload_used: '<script>alert("XSS Vulnerability Found")</script>',
        response_received: 'تم عرض الكود JavaScript في الصفحة بدون تنظيف',
        impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
        evidence_found: 'ظهور نافذة alert تؤكد تنفيذ الكود',
        injection_point: 'query parameter في GET request',
        exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
        browser_info: 'Chrome 91.0, Firefox 89.0 - vulnerable in both',
        session_impact: 'يمكن سرقة session tokens والتحكم في الحساب',
        technical_details: 'Reflected XSS vulnerability in search functionality',
        business_impact: 'User session hijacking and data theft potential',
        security_implications: 'Client-side code execution and user impersonation'
    },
    'CSRF': {
        payload_used: '<form action="https://example.com/change-password.php" method="post"><input name="new_password" value="hacked123"></form>',
        response_received: 'تم تغيير كلمة المرور بنجاح بدون تحقق من CSRF token',
        impact_observed: 'تغيير كلمة مرور المستخدم من موقع خارجي',
        evidence_found: 'عدم وجود CSRF protection في النموذج',
        injection_point: 'change-password form عبر external website',
        exploitation_result: 'تم تغيير كلمة المرور إلى "hacked123"',
        attack_vector: 'malicious website hosting the CSRF form',
        protection_missing: 'no CSRF tokens, no referrer validation',
        technical_details: 'Cross-Site Request Forgery in password change functionality',
        business_impact: 'Unauthorized account modifications and potential account takeover',
        security_implications: 'User actions can be performed without consent'
    }
};

async function testExportedReportsComprehensive() {
    console.log('🧪 بدء اختبار التقارير المُصدرة الشامل...');
    
    try {
        // التحقق من وجود BugBountyCore
        if (typeof BugBountyCore === 'undefined') {
            throw new Error('BugBountyCore غير محمل');
        }
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // تطبيق الدوال الـ36 على جميع الثغرات
        console.log('🔥 تطبيق الدوال الـ36 والملفات الشاملة على جميع الثغرات...');
        
        for (let i = 0; i < comprehensiveTestVulnerabilities.length; i++) {
            const vuln = comprehensiveTestVulnerabilities[i];
            const realData = comprehensiveRealDataByType[vuln.type];
            
            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            // تطبيق جميع الدوال الـ36 والملفات الشاملة
            await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
            
            console.log(`✅ تم تطبيق الدوال على: ${vuln.name}`);
        }
        
        // اختبار التقرير الرئيسي
        console.log('📋 اختبار التقرير الرئيسي المُصدر...');
        const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(comprehensiveTestVulnerabilities);
        
        // تحليل التقرير الرئيسي
        const mainReportAnalysis = analyzeExportedReport(mainReportHTML, 'التقرير الرئيسي');
        console.log('📊 تحليل التقرير الرئيسي:', mainReportAnalysis);
        
        // اختبار التقرير المنفصل
        console.log('📄 اختبار التقرير المنفصل المُصدر...');
        const separateReportHTML = await bugBountyCore.formatSinglePageReport({
            page_name: 'صفحة الاختبار الشامل',
            page_url: 'https://example.com/comprehensive-test',
            vulnerabilities: comprehensiveTestVulnerabilities
        });
        
        // تحليل التقرير المنفصل
        const separateReportAnalysis = analyzeExportedReport(separateReportHTML, 'التقرير المنفصل');
        console.log('📊 تحليل التقرير المنفصل:', separateReportAnalysis);
        
        // مقارنة التقارير المُصدرة
        const exportComparison = compareExportedReports(mainReportAnalysis, separateReportAnalysis);
        console.log('🔍 مقارنة التقارير المُصدرة:', exportComparison);
        
        // اختبار التصدير الفعلي
        console.log('💾 اختبار التصدير الفعلي للتقارير...');
        const exportTest = await testActualExport(bugBountyCore, comprehensiveTestVulnerabilities);
        
        // النتيجة النهائية الشاملة
        const finalResult = {
            success: true,
            mainReport: {
                size: mainReportHTML.length,
                analysis: mainReportAnalysis,
                sample: mainReportHTML.substring(0, 800) + '...',
                exportable: mainReportHTML.length > 5000
            },
            separateReport: {
                size: separateReportHTML.length,
                analysis: separateReportAnalysis,
                sample: separateReportHTML.substring(0, 800) + '...',
                exportable: separateReportHTML.length > 2000
            },
            comparison: exportComparison,
            exportTest: exportTest,
            vulnerabilities_processed: comprehensiveTestVulnerabilities.length,
            functions_applied: 36,
            comprehensive_files_used: 10,
            overall_quality: calculateOverallQuality(mainReportAnalysis, separateReportAnalysis, exportComparison),
            timestamp: new Date().toISOString()
        };
        
        console.log('🎉 اكتمل اختبار التقارير المُصدرة الشامل!');
        return finalResult;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التقارير المُصدرة:', error);
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// دالة تحليل التقرير المُصدر
function analyzeExportedReport(reportHTML, reportType) {
    const analysis = {
        type: reportType,
        totalSize: reportHTML.length,
        hasComprehensiveStructure: false,
        hasAllFunctionGroups: false,
        hasComprehensiveFiles: false,
        hasSystemSummary: false,
        isWellFormatted: false,
        isNotOverlapping: false,
        hasRealDynamicContent: false,
        hasProperCSS: false,
        contentQuality: 'unknown',
        structureScore: 0,
        issues: []
    };
    
    // فحص البنية الشاملة
    const comprehensiveIndicators = [
        'النظام v4.0 - جميع الدوال الـ36 والملفات الشاملة',
        'المجموعة الأولى: الدوال الأساسية الشاملة',
        'المجموعة الثانية: دوال التحليل المتقدم',
        'المجموعة الثالثة: دوال التحليل التفاعلي والبصري',
        'المجموعة الرابعة: دوال النظام المثابر والأدلة',
        'المجموعة الخامسة: دوال التحليل المتقدم والخبراء'
    ];
    
    let structureCount = 0;
    comprehensiveIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            structureCount++;
        }
    });
    
    analysis.hasComprehensiveStructure = structureCount >= 4;
    analysis.hasAllFunctionGroups = structureCount >= 5;
    
    // فحص الملفات الشاملة
    const comprehensiveFiles = [
        'الملفات الشاملة التفصيلية من النظام v4',
        'impact_visualizer.js',
        'textual_impact_analyzer.js'
    ];
    
    let filesCount = 0;
    comprehensiveFiles.forEach(file => {
        if (reportHTML.includes(file)) {
            filesCount++;
        }
    });
    
    analysis.hasComprehensiveFiles = filesCount >= 2;
    
    // فحص ملخص النظام
    analysis.hasSystemSummary = reportHTML.includes('ملخص شامل لحالة النظام v4.0');
    
    // فحص التنسيق
    const formatIndicators = [
        'style="background: linear-gradient',
        'border-radius:',
        'padding:',
        'margin:'
    ];
    
    let formatCount = 0;
    formatIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            formatCount++;
        }
    });
    
    analysis.isWellFormatted = formatCount >= 3;
    analysis.hasProperCSS = formatCount >= 4;
    
    // فحص عدم التداخل
    const divOpenCount = (reportHTML.match(/<div[^>]*>/g) || []).length;
    const divCloseCount = (reportHTML.match(/<\/div>/g) || []).length;
    analysis.isNotOverlapping = Math.abs(divOpenCount - divCloseCount) <= 2;
    
    if (!analysis.isNotOverlapping) {
        analysis.issues.push(`عدم توازن في عناصر div: ${divOpenCount} فتح، ${divCloseCount} إغلاق`);
    }
    
    // فحص المحتوى الديناميكي الحقيقي
    const realContentIndicators = [
        'MySQL Error',
        'alert("XSS")',
        'CSRF token',
        'admin\' OR \'1\'=\'1\'',
        'exploitation_result',
        'technical_details'
    ];
    
    let realContentCount = 0;
    realContentIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            realContentCount++;
        }
    });
    
    analysis.hasRealDynamicContent = realContentCount >= 3;
    
    // فحص الرسائل العامة (يجب ألا تكون موجودة)
    const genericMessages = [
        'تم تطبيق الدالة بنجاح',
        'تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح'
    ];
    
    genericMessages.forEach(msg => {
        if (reportHTML.includes(msg)) {
            analysis.issues.push(`يحتوي على رسالة عامة: ${msg}`);
        }
    });
    
    // حساب نقاط البنية
    analysis.structureScore = [
        analysis.hasComprehensiveStructure,
        analysis.hasAllFunctionGroups,
        analysis.hasComprehensiveFiles,
        analysis.hasSystemSummary,
        analysis.isWellFormatted,
        analysis.isNotOverlapping,
        analysis.hasRealDynamicContent,
        analysis.hasProperCSS
    ].filter(Boolean).length;
    
    // تقييم جودة المحتوى
    if (analysis.structureScore >= 7) {
        analysis.contentQuality = 'excellent';
    } else if (analysis.structureScore >= 5) {
        analysis.contentQuality = 'good';
    } else if (analysis.structureScore >= 3) {
        analysis.contentQuality = 'fair';
    } else {
        analysis.contentQuality = 'poor';
    }
    
    return analysis;
}

// دالة مقارنة التقارير المُصدرة
function compareExportedReports(mainAnalysis, separateAnalysis) {
    return {
        sizeDifference: Math.abs(mainAnalysis.totalSize - separateAnalysis.totalSize),
        qualityMatch: mainAnalysis.contentQuality === separateAnalysis.contentQuality,
        structureScoreMatch: Math.abs(mainAnalysis.structureScore - separateAnalysis.structureScore) <= 1,
        bothHaveComprehensiveStructure: mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure,
        bothHaveRealContent: mainAnalysis.hasRealDynamicContent && separateAnalysis.hasRealDynamicContent,
        bothWellFormatted: mainAnalysis.isWellFormatted && separateAnalysis.isWellFormatted,
        bothNotOverlapping: mainAnalysis.isNotOverlapping && separateAnalysis.isNotOverlapping,
        issuesComparison: {
            main: mainAnalysis.issues.length,
            separate: separateAnalysis.issues.length
        },
        overallCompatibility: calculateCompatibility(mainAnalysis, separateAnalysis)
    };
}

// دالة حساب التوافق
function calculateCompatibility(mainAnalysis, separateAnalysis) {
    const compatibilityFactors = [
        mainAnalysis.contentQuality === separateAnalysis.contentQuality,
        Math.abs(mainAnalysis.structureScore - separateAnalysis.structureScore) <= 1,
        mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure,
        mainAnalysis.hasRealDynamicContent && separateAnalysis.hasRealDynamicContent,
        mainAnalysis.isWellFormatted && separateAnalysis.isWellFormatted,
        mainAnalysis.isNotOverlapping && separateAnalysis.isNotOverlapping
    ];
    
    const compatibilityScore = compatibilityFactors.filter(Boolean).length;
    
    if (compatibilityScore >= 5) return 'excellent';
    if (compatibilityScore >= 4) return 'good';
    if (compatibilityScore >= 2) return 'fair';
    return 'poor';
}

// دالة اختبار التصدير الفعلي
async function testActualExport(bugBountyCore, vulnerabilities) {
    try {
        // محاولة تصدير التقرير الرئيسي
        const mainExportResult = await bugBountyCore.exportReport(vulnerabilities, 'main');
        
        // محاولة تصدير التقرير المنفصل
        const separateExportResult = await bugBountyCore.exportReport(vulnerabilities, 'separate');
        
        return {
            mainExportSuccess: !!mainExportResult,
            separateExportSuccess: !!separateExportResult,
            mainExportSize: mainExportResult ? mainExportResult.length : 0,
            separateExportSize: separateExportResult ? separateExportResult.length : 0,
            exportFunctional: !!(mainExportResult && separateExportResult)
        };
    } catch (error) {
        return {
            mainExportSuccess: false,
            separateExportSuccess: false,
            exportError: error.message,
            exportFunctional: false
        };
    }
}

// دالة حساب الجودة الإجمالية
function calculateOverallQuality(mainAnalysis, separateAnalysis, comparison) {
    const qualityFactors = [
        mainAnalysis.contentQuality === 'excellent' || mainAnalysis.contentQuality === 'good',
        separateAnalysis.contentQuality === 'excellent' || separateAnalysis.contentQuality === 'good',
        comparison.overallCompatibility === 'excellent' || comparison.overallCompatibility === 'good',
        comparison.bothHaveComprehensiveStructure,
        comparison.bothHaveRealContent,
        comparison.bothWellFormatted,
        comparison.bothNotOverlapping
    ];
    
    const qualityScore = qualityFactors.filter(Boolean).length;
    
    if (qualityScore >= 6) return 'excellent';
    if (qualityScore >= 4) return 'good';
    if (qualityScore >= 2) return 'fair';
    return 'poor';
}

// تصدير الدالة للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testExportedReportsComprehensive };
} else if (typeof window !== 'undefined') {
    window.testExportedReportsComprehensive = testExportedReportsComprehensive;
}

console.log('📋 ملف اختبار التقارير المُصدرة الشامل جاهز');
console.log('🚀 استخدم: testExportedReportsComprehensive() لبدء الاختبار');
