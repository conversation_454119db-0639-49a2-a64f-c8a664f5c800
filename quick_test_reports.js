/**
 * اختبار سريع للتقارير - يمكن تشغيله من PowerShell مباشرة
 * للتحقق السريع من حالة النظام والدوال الـ36
 */

// تشغيل الاختبار السريع
async function quickTestReports() {
    console.log('🚀 بدء الاختبار السريع للتقارير...');
    
    const results = {
        timestamp: new Date().toISOString(),
        bugBountyCoreLoaded: false,
        functionsFound: 0,
        templateWorking: false,
        mainReportGenerated: false,
        separateReportGenerated: false,
        contentQuality: 'unknown',
        issues: [],
        recommendations: []
    };
    
    try {
        // فحص تحميل BugBountyCore
        if (typeof BugBountyCore !== 'undefined') {
            results.bugBountyCoreLoaded = true;
            console.log('✅ BugBountyCore محمل بنجاح');
            
            const bugBountyCore = new BugBountyCore();
            
            // فحص الدوال الأساسية
            const coreFunctions = [
                'generateComprehensiveDetailsFromRealData',
                'generateComprehensiveVulnerabilityAnalysis',
                'generateDynamicSecurityImpactAnalysis',
                'applyAllComprehensiveFunctionsToVulnerability',
                'generateVulnerabilitiesHTML'
            ];
            
            coreFunctions.forEach(funcName => {
                if (typeof bugBountyCore[funcName] === 'function') {
                    results.functionsFound++;
                    console.log(`✅ الدالة موجودة: ${funcName}`);
                } else {
                    console.log(`❌ الدالة مفقودة: ${funcName}`);
                    results.issues.push(`الدالة مفقودة: ${funcName}`);
                }
            });
            
            // فحص القالب
            if (typeof bugBountyCore.getEmbeddedReportTemplate === 'function') {
                const template = bugBountyCore.getEmbeddedReportTemplate();
                if (template && template.includes('{{VULNERABILITIES_CONTENT}}')) {
                    results.templateWorking = true;
                    console.log('✅ القالب الشامل يعمل بشكل صحيح');
                } else {
                    console.log('❌ مشكلة في القالب الشامل');
                    results.issues.push('مشكلة في القالب الشامل');
                }
            }
            
            // اختبار سريع للتقرير الرئيسي
            try {
                const testVuln = {
                    name: 'اختبار سريع',
                    type: 'Test',
                    severity: 'Medium',
                    url: 'https://test.com',
                    parameter: 'test'
                };
                
                const testData = {
                    payload_used: 'test_payload',
                    response_received: 'test_response',
                    impact_observed: 'test_impact'
                };
                
                // تطبيق الدوال
                await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(testVuln, testData);
                
                // إنشاء التقرير الرئيسي
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                
                if (mainReport && mainReport.length > 1000) {
                    results.mainReportGenerated = true;
                    console.log(`✅ التقرير الرئيسي تم إنشاؤه (${mainReport.length} حرف)`);
                    
                    // فحص جودة المحتوى
                    if (mainReport.includes('comprehensive_details') && 
                        !mainReport.includes('تم تطبيق الدالة بنجاح')) {
                        results.contentQuality = 'good';
                        console.log('✅ جودة المحتوى جيدة');
                    } else if (mainReport.includes('تم تطبيق الدالة بنجاح')) {
                        results.contentQuality = 'poor';
                        console.log('⚠️ يحتوي على رسائل عامة');
                        results.issues.push('يحتوي على رسائل عامة');
                    } else {
                        results.contentQuality = 'fair';
                        console.log('⚠️ جودة المحتوى متوسطة');
                    }
                } else {
                    console.log('❌ فشل في إنشاء التقرير الرئيسي');
                    results.issues.push('فشل في إنشاء التقرير الرئيسي');
                }
                
                // اختبار التقرير المنفصل
                if (typeof bugBountyCore.formatSinglePageReport === 'function') {
                    const separateReport = await bugBountyCore.formatSinglePageReport({
                        page_name: 'اختبار سريع',
                        page_url: 'https://test.com',
                        vulnerabilities: [testVuln]
                    });
                    
                    if (separateReport && separateReport.length > 500) {
                        results.separateReportGenerated = true;
                        console.log(`✅ التقرير المنفصل تم إنشاؤه (${separateReport.length} حرف)`);
                    } else {
                        console.log('❌ فشل في إنشاء التقرير المنفصل');
                        results.issues.push('فشل في إنشاء التقرير المنفصل');
                    }
                }
                
            } catch (testError) {
                console.log('❌ خطأ في اختبار التقارير:', testError.message);
                results.issues.push(`خطأ في اختبار التقارير: ${testError.message}`);
            }
            
        } else {
            console.log('❌ BugBountyCore غير محمل');
            results.issues.push('BugBountyCore غير محمل');
        }
        
        // إنشاء التوصيات
        if (results.issues.length === 0) {
            results.recommendations.push('النظام يعمل بشكل ممتاز!');
        } else {
            if (!results.bugBountyCoreLoaded) {
                results.recommendations.push('تحقق من تحميل ملف BugBountyCore.js');
            }
            if (results.functionsFound < 5) {
                results.recommendations.push('تحقق من وجود جميع الدوال المطلوبة');
            }
            if (!results.templateWorking) {
                results.recommendations.push('تحقق من القالب الشامل');
            }
            if (results.contentQuality === 'poor') {
                results.recommendations.push('تحسين جودة المحتوى المُنتج من الدوال');
            }
        }
        
        // طباعة النتائج
        console.log('\n📊 ملخص النتائج:');
        console.log(`✅ BugBountyCore محمل: ${results.bugBountyCoreLoaded ? 'نعم' : 'لا'}`);
        console.log(`✅ الدوال الموجودة: ${results.functionsFound}/5`);
        console.log(`✅ القالب يعمل: ${results.templateWorking ? 'نعم' : 'لا'}`);
        console.log(`✅ التقرير الرئيسي: ${results.mainReportGenerated ? 'تم إنشاؤه' : 'فشل'}`);
        console.log(`✅ التقرير المنفصل: ${results.separateReportGenerated ? 'تم إنشاؤه' : 'فشل'}`);
        console.log(`✅ جودة المحتوى: ${results.contentQuality}`);
        
        if (results.issues.length > 0) {
            console.log('\n⚠️ المشاكل المكتشفة:');
            results.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
        }
        
        if (results.recommendations.length > 0) {
            console.log('\n💡 التوصيات:');
            results.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. ${rec}`);
            });
        }
        
        return results;
        
    } catch (error) {
        console.log('❌ خطأ عام في الاختبار:', error.message);
        results.issues.push(`خطأ عام: ${error.message}`);
        return results;
    }
}

// تشغيل الاختبار إذا كان في بيئة المتصفح
if (typeof window !== 'undefined') {
    window.quickTestReports = quickTestReports;
    
    // تشغيل تلقائي بعد تحميل الصفحة
    window.addEventListener('load', async function() {
        console.log('🌐 بدء الاختبار السريع التلقائي...');
        await quickTestReports();
    });
}

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { quickTestReports };
}

console.log('📋 ملف الاختبار السريع جاهز');
