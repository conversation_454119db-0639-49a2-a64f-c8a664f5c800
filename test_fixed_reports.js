/**
 * اختبار سريع للتأكد من أن الدالة المُصلحة تعمل
 */

const fs = require('fs');

console.log('🧪 اختبار الدالة المُصلحة generateVulnerabilitiesHTML...');

// تحميل BugBountyCore الحقيقي
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة شاملة للمتصفح
    global.window = {
        addEventListener: () => {},
        removeEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test Environment' },
        document: null
    };
    
    global.document = {
        createElement: (tag) => ({
            style: {},
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null,
            innerHTML: '',
            textContent: '',
            tagName: tag.toUpperCase()
        }),
        body: { 
            appendChild: () => {},
            style: {}
        },
        head: {
            appendChild: () => {},
            style: {}
        },
        getElementById: () => null,
        querySelector: () => null,
        querySelectorAll: () => [],
        addEventListener: () => {},
        removeEventListener: () => {}
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.sessionStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.console = console;
    global.alert = () => {};
    global.confirm = () => true;
    global.prompt = () => '';
    global.window.document = global.document;
    
    // تنفيذ الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore || window.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore الحقيقي بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    console.error('❌ تفاصيل الخطأ:', error.stack);
    process.exit(1);
}

// بيانات ثغرة للاختبار
const testVulnerability = {
    name: 'SQL Injection في نموذج تسجيل الدخول',
    type: 'SQL Injection',
    severity: 'Critical',
    url: 'https://example.com/login.php',
    parameter: 'username',
    payload: "admin' OR '1'='1' --"
};

async function runQuickTest() {
    try {
        console.log('🧪 بدء الاختبار السريع...');
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // اختبار الدالة المُصلحة
        console.log('🔥 اختبار generateVulnerabilitiesHTML المُصلحة...');
        
        const result = await bugBountyCore.generateVulnerabilitiesHTML([testVulnerability]);
        
        console.log('\n📊 نتائج الاختبار:');
        console.log('='.repeat(50));
        console.log(`📏 حجم النتيجة: ${result.length} حرف`);
        console.log(`🔍 يحتوي على القالب الشامل: ${result.includes('النظام v4.0') ? '✅' : '❌'}`);
        console.log(`🔍 يحتوي على الدوال الـ36: ${result.includes('المجموعة الأولى') ? '✅' : '❌'}`);
        console.log(`🔍 يحتوي على البيانات الحقيقية: ${result.includes("admin' OR '1'='1'") ? '✅' : '❌'}`);
        console.log(`🔍 لا يحتوي على نصوص عامة: ${!result.includes('تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح') ? '✅' : '❌'}`);
        
        // حفظ النتيجة للفحص
        fs.writeFileSync('test_fixed_result.html', result, 'utf8');
        console.log('\n💾 تم حفظ النتيجة في: test_fixed_result.html');
        
        // عرض أول 500 حرف من النتيجة
        console.log('\n📝 أول 500 حرف من النتيجة:');
        console.log('-'.repeat(50));
        console.log(result.substring(0, 500));
        console.log('-'.repeat(50));
        
        if (result.length > 5000 && result.includes('النظام v4.0') && !result.includes('تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح')) {
            console.log('\n🎉 النتيجة: الدالة المُصلحة تعمل بنجاح!');
            console.log('✅ تستخدم القالب الشامل v4.0');
            console.log('✅ تطبق الدوال الـ36 بشكل صحيح');
            console.log('✅ لا تحتوي على نصوص عامة');
            return true;
        } else {
            console.log('\n⚠️ النتيجة: الدالة تحتاج مزيد من الإصلاح');
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return false;
    }
}

// تشغيل الاختبار
runQuickTest().then(success => {
    if (success) {
        console.log('\n🌟 الاختبار نجح! الدالة المُصلحة تعمل بشكل صحيح');
        process.exit(0);
    } else {
        console.log('\n❌ الاختبار فشل! الدالة تحتاج مزيد من الإصلاح');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ خطأ عام:', error);
    process.exit(1);
});
