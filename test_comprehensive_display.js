/**
 * اختبار العرض الشامل التفصيلي الجديد للدوال الـ36 والملفات الشاملة
 * للتحقق من أن العرض منسق وصحيح وليس متداخل
 */

console.log('🔥 بدء اختبار العرض الشامل التفصيلي الجديد...');

async function testComprehensiveDisplay() {
    try {
        // التحقق من وجود BugBountyCore
        if (typeof BugBountyCore === 'undefined') {
            throw new Error('BugBountyCore غير محمل');
        }
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // إنشاء ثغرة تجريبية مع بيانات شاملة
        const testVuln = {
            name: 'SQL Injection في نموذج تسجيل الدخول - اختبار العرض الشامل',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --",
            location: 'Login Form',
            method: 'POST'
        };
        
        // بيانات حقيقية شاملة للاختبار
        const comprehensiveRealData = {
            payload_used: "admin' OR '1'='1' --",
            response_received: 'MySQL Error: You have an error in your SQL syntax near OR at line 1',
            impact_observed: 'تم تجاوز المصادقة بنجاح والوصول لحساب المدير',
            evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL والجداول',
            injection_point: 'username parameter في POST request',
            exploitation_result: 'تم الدخول كمدير بدون كلمة مرور صحيحة',
            database_info: 'MySQL 5.7.33, database: webapp_db, table: users',
            extracted_data: 'usernames, password hashes, email addresses',
            technical_details: 'SQL injection vulnerability in authentication bypass',
            business_impact: 'Complete compromise of user authentication system',
            security_implications: 'Unauthorized access to all user accounts',
            exploitation_complexity: 'Low - simple SQL injection payload',
            verification_steps: 'Confirmed by successful admin login without credentials'
        };
        
        console.log('🔧 تطبيق جميع الدوال الـ36 والملفات الشاملة...');
        
        // تطبيق جميع الدوال الـ36 والملفات الشاملة
        await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(testVuln, comprehensiveRealData);
        
        console.log('📊 فحص البيانات المُنتجة من الدوال...');
        
        // فحص البيانات المُنتجة
        const producedData = {
            comprehensive_details: testVuln.comprehensive_details,
            comprehensive_analysis: testVuln.comprehensive_analysis,
            security_impact_analysis: testVuln.security_impact_analysis,
            exploitation_steps: testVuln.exploitation_steps,
            dynamic_recommendations: testVuln.dynamic_recommendations,
            realtime_assessment: testVuln.realtime_assessment,
            risk_analysis: testVuln.risk_analysis,
            threat_modeling: testVuln.threat_modeling,
            dynamic_impact: testVuln.dynamic_impact,
            advanced_exploitation: testVuln.advanced_exploitation,
            interactive_dialogue: testVuln.interactive_dialogue,
            visual_changes: testVuln.visual_changes,
            persistent_results: testVuln.persistent_results,
            real_evidence: testVuln.real_evidence,
            expert_analysis: testVuln.expert_analysis,
            impact_visualization: testVuln.impact_visualization,
            textual_impact_analysis: testVuln.textual_impact_analysis
        };
        
        console.log('📋 إنشاء HTML باستخدام العرض الشامل الجديد...');
        
        // إنشاء HTML باستخدام العرض الشامل الجديد
        const comprehensiveHTML = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
        
        console.log('🔍 تحليل العرض المُنتج...');
        
        // تحليل العرض المُنتج
        const analysis = {
            totalSize: comprehensiveHTML.length,
            hasComprehensiveStructure: comprehensiveHTML.includes('النظام v4.0 - جميع الدوال الـ36 والملفات الشاملة'),
            hasFunctionGroups: comprehensiveHTML.includes('المجموعة الأولى: الدوال الأساسية الشاملة'),
            hasAllFiveGroups: [
                'المجموعة الأولى: الدوال الأساسية الشاملة',
                'المجموعة الثانية: دوال التحليل المتقدم',
                'المجموعة الثالثة: دوال التحليل التفاعلي والبصري',
                'المجموعة الرابعة: دوال النظام المثابر والأدلة',
                'المجموعة الخامسة: دوال التحليل المتقدم والخبراء'
            ].every(group => comprehensiveHTML.includes(group)),
            hasComprehensiveFiles: comprehensiveHTML.includes('الملفات الشاملة التفصيلية من النظام v4'),
            hasSystemSummary: comprehensiveHTML.includes('ملخص شامل لحالة النظام v4.0'),
            hasProperFormatting: comprehensiveHTML.includes('style="background: linear-gradient'),
            isWellStructured: !comprehensiveHTML.includes('undefined') && !comprehensiveHTML.includes('null'),
            hasRealContent: !comprehensiveHTML.includes('تم تطبيق الدالة بنجاح'),
            functionsDisplayed: Object.keys(producedData).filter(key => producedData[key]).length
        };
        
        console.log('📊 نتائج التحليل:');
        console.log(`   📏 حجم HTML: ${analysis.totalSize.toLocaleString()} حرف`);
        console.log(`   🏗️ بنية شاملة: ${analysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال: ${analysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   🔢 جميع المجموعات الخمس: ${analysis.hasAllFiveGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${analysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${analysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق صحيح: ${analysis.hasProperFormatting ? '✅' : '❌'}`);
        console.log(`   🏗️ بنية سليمة: ${analysis.isWellStructured ? '✅' : '❌'}`);
        console.log(`   📝 محتوى حقيقي: ${analysis.hasRealContent ? '✅' : '❌'}`);
        console.log(`   🔧 دوال مُطبقة: ${analysis.functionsDisplayed}/17`);
        
        // تقييم الجودة الإجمالية
        const qualityScore = [
            analysis.hasComprehensiveStructure,
            analysis.hasFunctionGroups,
            analysis.hasAllFiveGroups,
            analysis.hasComprehensiveFiles,
            analysis.hasSystemSummary,
            analysis.hasProperFormatting,
            analysis.isWellStructured,
            analysis.hasRealContent
        ].filter(Boolean).length;
        
        console.log(`\n🎯 نقاط الجودة: ${qualityScore}/8`);
        
        if (qualityScore >= 7) {
            console.log('🎉 العرض الشامل التفصيلي يعمل بشكل ممتاز!');
        } else if (qualityScore >= 5) {
            console.log('✅ العرض الشامل التفصيلي يعمل بشكل جيد');
        } else {
            console.log('⚠️ العرض الشامل التفصيلي يحتاج تحسين');
        }
        
        // عرض عينة من المحتوى
        console.log('\n📝 عينة من المحتوى المُنتج:');
        console.log(comprehensiveHTML.substring(0, 1000) + '...');
        
        return {
            success: true,
            analysis: analysis,
            qualityScore: qualityScore,
            sampleContent: comprehensiveHTML.substring(0, 1000),
            fullContentSize: comprehensiveHTML.length,
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار العرض الشامل:', error);
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// تصدير الدالة للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testComprehensiveDisplay };
} else if (typeof window !== 'undefined') {
    window.testComprehensiveDisplay = testComprehensiveDisplay;
}

console.log('📋 ملف اختبار العرض الشامل التفصيلي جاهز');
console.log('🚀 استخدم: testComprehensiveDisplay() لبدء الاختبار');
