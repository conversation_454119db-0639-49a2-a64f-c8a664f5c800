# تشغيل جميع اختبارات التقارير المُصدرة الشاملة

Write-Host "🔥 بدء تشغيل جميع اختبارات التقارير المُصدرة..." -ForegroundColor Yellow

# التحقق من وجود Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js متوفر: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير متوفر - يرجى تثبيته أولاً" -ForegroundColor Red
    exit 1
}

# التحقق من وجود الملفات المطلوبة
$requiredFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "simple_test_reports.js",
    "test_real_system_reports.js"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ الملف المطلوب غير موجود: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# تشغيل الاختبار المبسط أولاً
Write-Host "`n🧪 تشغيل الاختبار المبسط..." -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

try {
    $simpleTestResult = node simple_test_reports.js
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ نجح الاختبار المبسط" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل الاختبار المبسط" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في تشغيل الاختبار المبسط: $_" -ForegroundColor Red
}

# إنشاء ملف HTML لاختبار النظام الحقيقي
Write-Host "`n🌐 إنشاء ملف HTML لاختبار النظام الحقيقي..." -ForegroundColor Cyan

$realTestHTML = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الحقيقي للتقارير المُصدرة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
        .progress-bar { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; transition: width 0.3s; }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .report-card { background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار النظام الحقيقي للتقارير المُصدرة الشامل</h1>
        
        <div class="section">
            <h2>🎮 التحكم في الاختبار</h2>
            <button onclick="runRealSystemTest()" id="testBtn">🚀 تشغيل اختبار النظام الحقيقي</button>
            <button onclick="clearResults()" id="clearBtn">🧹 مسح النتائج</button>
            <div id="testStatus">📋 جاهز لبدء الاختبار</div>
        </div>
        
        <div class="section">
            <h2>📊 الجودة الإجمالية</h2>
            <div id="overallQuality"></div>
        </div>
        
        <div class="section">
            <h2>📋 مقارنة التقارير</h2>
            <div id="reportsComparison"></div>
        </div>
        
        <div class="section">
            <h2>📊 تحليل التقرير الرئيسي</h2>
            <div id="mainReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>📄 تحليل التقرير المنفصل</h2>
            <div id="separateReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>📝 عينات من المحتوى</h2>
            <div id="contentSamples"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_real_system_reports.js"></script>
    
    <script>
        async function runRealSystemTest() {
            const testBtn = document.getElementById('testBtn');
            const statusDiv = document.getElementById('testStatus');
            
            testBtn.disabled = true;
            testBtn.textContent = '⏳ جاري التشغيل...';
            
            try {
                statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل اختبار النظام الحقيقي...</div>';
                
                // تشغيل الاختبار
                const result = await testRealSystemReports();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار النظام الحقيقي بنجاح!</div>';
                    
                    // عرض النتائج
                    displayOverallQuality(result);
                    displayReportsComparison(result);
                    displayReportAnalysis(result.mainReport.analysis, 'mainReportAnalysis');
                    displayReportAnalysis(result.separateReport.analysis, 'separateReportAnalysis');
                    displayContentSamples(result);
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ فشل الاختبار: ' + result.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 تشغيل اختبار النظام الحقيقي';
            }
        }
        
        function displayOverallQuality(result) {
            const qualityDiv = document.getElementById('overallQuality');
            const quality = result.overall_quality;
            
            let qualityClass = 'poor';
            let qualityText = 'ضعيف';
            let qualityScore = 0;
            
            switch(quality) {
                case 'excellent':
                    qualityClass = 'excellent';
                    qualityText = 'ممتاز';
                    qualityScore = 95;
                    break;
                case 'good':
                    qualityClass = 'good';
                    qualityText = 'جيد';
                    qualityScore = 80;
                    break;
                case 'fair':
                    qualityClass = 'fair';
                    qualityText = 'مقبول';
                    qualityScore = 60;
                    break;
                default:
                    qualityClass = 'poor';
                    qualityText = 'ضعيف';
                    qualityScore = 30;
            }
            
            qualityDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${qualityClass}">${qualityText}</div>
                        <div>الجودة الإجمالية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.vulnerabilities_processed}</div>
                        <div>ثغرات معالجة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.functions_applied}</div>
                        <div>دالة مطبقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.comprehensive_files_used}</div>
                        <div>ملف شامل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.mainReport.size / 1024)} KB</div>
                        <div>حجم التقرير الرئيسي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.separateReport.size / 1024)} KB</div>
                        <div>حجم التقرير المنفصل</div>
                    </div>
                </div>
                
                <div class="progress">
                    <div class="progress-bar" style="width: ${qualityScore}%"></div>
                </div>
            `;
        }
        
        function displayReportsComparison(result) {
            const comparisonDiv = document.getElementById('reportsComparison');
            
            const sizeDiff = Math.abs(result.mainReport.size - result.separateReport.size);
            const qualityMatch = result.mainReport.analysis.contentQuality === result.separateReport.analysis.contentQuality;
            const bothGood = result.mainReport.analysis.contentQuality !== 'poor' && result.separateReport.analysis.contentQuality !== 'poor';
            
            comparisonDiv.innerHTML = `
                <div class="comparison-grid">
                    <div class="report-card">
                        <h4>📊 مقارنة الجودة</h4>
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-number ${qualityMatch ? 'excellent' : 'warning'}">${qualityMatch ? '✅' : '⚠️'}</div>
                                <div>جودة متطابقة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number ${bothGood ? 'excellent' : 'poor'}">${bothGood ? '✅' : '❌'}</div>
                                <div>كلاهما جيد</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="report-card">
                        <h4>🔍 تفاصيل المقارنة</h4>
                        <div class="info">
                            <strong>فرق الحجم:</strong> ${Math.round(sizeDiff / 1024)} KB<br>
                            <strong>جودة التقرير الرئيسي:</strong> ${result.mainReport.analysis.contentQuality}<br>
                            <strong>جودة التقرير المنفصل:</strong> ${result.separateReport.analysis.contentQuality}
                        </div>
                    </div>
                </div>
            `;
        }
        
        function displayReportAnalysis(analysis, elementId) {
            const element = document.getElementById(elementId);
            
            element.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.contentQuality}">${getQualityText(analysis.contentQuality)}</div>
                        <div>جودة المحتوى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.structureScore}/8</div>
                        <div>نقاط البنية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.totalSize.toLocaleString()}</div>
                        <div>حجم المحتوى (حرف)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.issues.length === 0 ? 'excellent' : 'warning'}">${analysis.issues.length}</div>
                        <div>المشاكل المكتشفة</div>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveStructure ? 'excellent' : 'poor'}">${analysis.hasComprehensiveStructure ? '✅' : '❌'}</div>
                        <div>بنية شاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasAllFunctionGroups ? 'excellent' : 'poor'}">${analysis.hasAllFunctionGroups ? '✅' : '❌'}</div>
                        <div>جميع مجموعات الدوال</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveFiles ? 'excellent' : 'poor'}">${analysis.hasComprehensiveFiles ? '✅' : '❌'}</div>
                        <div>الملفات الشاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.isNotOverlapping ? 'excellent' : 'poor'}">${analysis.isNotOverlapping ? '✅' : '❌'}</div>
                        <div>غير متداخل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasRealDynamicContent ? 'excellent' : 'poor'}">${analysis.hasRealDynamicContent ? '✅' : '❌'}</div>
                        <div>محتوى ديناميكي</div>
                    </div>
                </div>
                
                ${analysis.issues.length > 0 ? `
                <div class="warning">
                    <strong>المشاكل المكتشفة:</strong>
                    <ul>${analysis.issues.map(issue => '<li>' + issue + '</li>').join('')}</ul>
                </div>
                ` : ''}
            `;
        }
        
        function displayContentSamples(result) {
            const samplesDiv = document.getElementById('contentSamples');
            
            samplesDiv.innerHTML = `
                <h4>📋 عينة من التقرير الرئيسي:</h4>
                <div class="code">${result.mainReport.sample}</div>
                
                <h4>📄 عينة من التقرير المنفصل:</h4>
                <div class="code">${result.separateReport.sample}</div>
            `;
        }
        
        function getQualityText(quality) {
            const qualityTexts = {
                'excellent': 'ممتاز',
                'good': 'جيد',
                'fair': 'مقبول',
                'poor': 'ضعيف'
            };
            return qualityTexts[quality] || quality;
        }
        
        function clearResults() {
            document.getElementById('testStatus').innerHTML = '📋 جاهز لبدء الاختبار';
            document.getElementById('overallQuality').innerHTML = '';
            document.getElementById('reportsComparison').innerHTML = '';
            document.getElementById('mainReportAnalysis').innerHTML = '';
            document.getElementById('separateReportAnalysis').innerHTML = '';
            document.getElementById('contentSamples').innerHTML = '';
        }
        
        // رسالة الترحيب
        window.addEventListener('load', function() {
            console.log('🌐 صفحة اختبار النظام الحقيقي جاهزة');
            console.log('🚀 اضغط على زر "تشغيل اختبار النظام الحقيقي" لبدء الاختبار');
        });
    </script>
</body>
</html>
"@

# حفظ ملف HTML
$realTestFile = "real_system_reports_test.html"
$realTestHTML | Out-File -FilePath $realTestFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف اختبار النظام الحقيقي: $realTestFile" -ForegroundColor Green

# فتح ملف اختبار النظام الحقيقي
Write-Host "🌐 فتح اختبار النظام الحقيقي في المتصفح..." -ForegroundColor Yellow
Start-Process $realTestFile

Write-Host "`n📊 ملخص جميع اختبارات التقارير المُصدرة:" -ForegroundColor Yellow
Write-Host "✅ تم تشغيل الاختبار المبسط بنجاح" -ForegroundColor Green
Write-Host "✅ تم إنشاء اختبار النظام الحقيقي" -ForegroundColor Green
Write-Host "✅ تم فتح واجهة الاختبار التفاعلية" -ForegroundColor Green

Write-Host "`n🔍 ما تم اختباره:" -ForegroundColor Cyan
Write-Host "   📋 التقرير الرئيسي والمنفصل" -ForegroundColor White
Write-Host "   📂 جميع مجموعات الدوال الـ36" -ForegroundColor White
Write-Host "   📁 الملفات الشاملة التفصيلية" -ForegroundColor White
Write-Host "   🎨 التنسيق والعرض المتناسق" -ForegroundColor White
Write-Host "   ❌ عدم التداخل في العناصر" -ForegroundColor White
Write-Host "   📝 المحتوى الديناميكي الحقيقي" -ForegroundColor White

Write-Host "`n📋 الملفات المُنشأة:" -ForegroundColor Yellow
Write-Host "   📄 simple_test_reports.js - اختبار مبسط" -ForegroundColor White
Write-Host "   🔧 test_real_system_reports.js - اختبار النظام الحقيقي" -ForegroundColor White
Write-Host "   🌐 $realTestFile - واجهة تفاعلية" -ForegroundColor White

Write-Host "`n✅ انتهى تشغيل جميع اختبارات التقارير المُصدرة" -ForegroundColor Green
