# Simple Test for Exported Report Content
Write-Host "Testing EXPORTED REPORT CONTENT..." -ForegroundColor Yellow

$results = @{
    ReportsFound = 0
    ComprehensiveContent = 0
    RealDataDisplay = 0
    Functions36Content = 0
    FilesContent = 0
    Score = 0
}

# Look for Bug Bounty reports
Write-Host "`nLooking for Bug Bounty reports..." -ForegroundColor Yellow
$reportFiles = Get-ChildItem -Path "." -Filter "*Bug_Bounty*" -File

if ($reportFiles.Count -eq 0) {
    Write-Host "No Bug Bounty reports found" -ForegroundColor Red
    Write-Host "Generate reports first, then test again" -ForegroundColor Yellow
} else {
    Write-Host "Found $($reportFiles.Count) Bug Bounty reports" -ForegroundColor Green
    $results.ReportsFound = $reportFiles.Count
    
    foreach ($file in $reportFiles) {
        Write-Host "`nAnalyzing: $($file.Name)" -ForegroundColor Cyan
        Write-Host "Size: $($file.Length) bytes" -ForegroundColor White
        
        $content = Get-Content $file.FullName -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
        
        if ($content) {
            # Check comprehensive content
            $comprehensiveItems = @(
                "comprehensive_description",
                "technical_details", 
                "exploitation_results",
                "impact_analysis"
            )
            
            $foundComprehensive = 0
            foreach ($item in $comprehensiveItems) {
                if ($content.Contains($item)) {
                    $foundComprehensive++
                    Write-Host "   ✅ $item" -ForegroundColor Green
                }
            }
            
            # Check real data display
            $realDataItems = @(
                "max-height: 200px",
                "JSON.stringify",
                "typeof",
                "comprehensive_details"
            )
            
            $foundRealData = 0
            foreach ($item in $realDataItems) {
                if ($content.Contains($item)) {
                    $foundRealData++
                    Write-Host "   ✅ $item" -ForegroundColor Green
                }
            }
            
            # Check functions
            $functionsItems = @(
                "generateComprehensiveDetailsFromRealData",
                "generateDynamicImpactForAnyVulnerability",
                "generateRealExploitationStepsForVulnerabilityComprehensive"
            )
            
            $foundFunctions = 0
            foreach ($item in $functionsItems) {
                if ($content.Contains($item)) {
                    $foundFunctions++
                    Write-Host "   ✅ $item" -ForegroundColor Green
                }
            }
            
            # Check files
            $filesItems = @(
                "impact_visualizer.js",
                "textual_impact_analyzer.js",
                "visual_impact_data"
            )
            
            $foundFiles = 0
            foreach ($item in $filesItems) {
                if ($content.Contains($item)) {
                    $foundFiles++
                    Write-Host "   ✅ $item" -ForegroundColor Green
                }
            }
            
            Write-Host "   Summary: Comprehensive($foundComprehensive/4) RealData($foundRealData/4) Functions($foundFunctions/3) Files($foundFiles/3)" -ForegroundColor White
            
            $results.ComprehensiveContent += $foundComprehensive
            $results.RealDataDisplay += $foundRealData
            $results.Functions36Content += $foundFunctions
            $results.FilesContent += $foundFiles
        }
    }
}

# Calculate score
$reportsScore = if ($results.ReportsFound -gt 0) { 25 } else { 0 }
$comprehensiveScore = [math]::Min(($results.ComprehensiveContent / 8) * 25, 25)
$realDataScore = [math]::Min(($results.RealDataDisplay / 8) * 25, 25)
$functionsScore = [math]::Min(($results.Functions36Content / 6) * 15, 15)
$filesScore = [math]::Min(($results.FilesContent / 6) * 10, 10)

$results.Score = $reportsScore + $comprehensiveScore + $realDataScore + $functionsScore + $filesScore

# Results
Write-Host "`nFINAL RESULTS:" -ForegroundColor Cyan
Write-Host "Reports Found: $($results.ReportsFound) ($reportsScore/25)" -ForegroundColor White
Write-Host "Comprehensive Content: $($results.ComprehensiveContent)/8 ($([math]::Round($comprehensiveScore, 1))/25)" -ForegroundColor White
Write-Host "Real Data Display: $($results.RealDataDisplay)/8 ($([math]::Round($realDataScore, 1))/25)" -ForegroundColor White
Write-Host "Functions Content: $($results.Functions36Content)/6 ($([math]::Round($functionsScore, 1))/15)" -ForegroundColor White
Write-Host "Files Content: $($results.FilesContent)/6 ($([math]::Round($filesScore, 1))/10)" -ForegroundColor White

Write-Host "`nTOTAL SCORE: $([math]::Round($results.Score, 1))/100" -ForegroundColor Yellow

if ($results.Score -ge 90) {
    Write-Host "EXCELLENT! Exported reports contain comprehensive content!" -ForegroundColor Green
} elseif ($results.Score -ge 70) {
    Write-Host "GOOD! Most comprehensive content is present!" -ForegroundColor Yellow
} elseif ($results.Score -ge 50) {
    Write-Host "PARTIAL! Some comprehensive content is present!" -ForegroundColor Yellow
} elseif ($results.ReportsFound -eq 0) {
    Write-Host "NO REPORTS! Generate reports first!" -ForegroundColor Red
} else {
    Write-Host "NEEDS WORK! Reports lack comprehensive content!" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
