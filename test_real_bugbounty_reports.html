<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الحقيقي للتقارير الشاملة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار النظام الحقيقي للتقارير الشاملة</h1>
        
        <div class="section">
            <h2>🎮 التحكم في الاختبار</h2>
            <button onclick="runRealTest()" id="testBtn">🚀 تشغيل اختبار النظام الحقيقي</button>
            <button onclick="clearResults()" id="clearBtn">🧹 مسح النتائج</button>
            <div id="testStatus">📋 جاهز لبدء الاختبار</div>
        </div>
        
        <div class="section">
            <h2>📊 نتائج المقارنة</h2>
            <div id="comparisonResults"></div>
        </div>
        
        <div class="section">
            <h2>📋 التقرير الرئيسي</h2>
            <div id="mainReportResults"></div>
        </div>
        
        <div class="section">
            <h2>📄 التقرير المنفصل</h2>
            <div id="separateReportResults"></div>
        </div>
        
        <div class="section">
            <h2>📝 عينات من المحتوى</h2>
            <div id="contentSamples"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    
    <script>
        // بيانات الاختبار
        const testVulnerabilities = [
            {
                name: 'SQL Injection في نموذج تسجيل الدخول',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'https://example.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --"
            },
            {
                name: 'Cross-Site Scripting في حقل البحث',
                type: 'XSS',
                severity: 'High',
                url: 'https://example.com/search.php',
                parameter: 'query',
                payload: '<script>alert("XSS")</script>'
            }
        ];
        
        const realDataByType = {
            'SQL Injection': {
                payload_used: "admin' OR '1'='1' --",
                response_received: 'MySQL Error: You have an error in your SQL syntax',
                impact_observed: 'تم تجاوز المصادقة بنجاح',
                exploitation_result: 'تم الدخول كمدير بدون كلمة مرور',
                technical_details: 'Boolean-based blind SQL injection vulnerability'
            },
            'XSS': {
                payload_used: '<script>alert("XSS")</script>',
                response_received: 'تم عرض الكود JavaScript في الصفحة',
                impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
                exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
                technical_details: 'Reflected XSS vulnerability in search functionality'
            }
        };
        
        async function runRealTest() {
            const testBtn = document.getElementById('testBtn');
            const statusDiv = document.getElementById('testStatus');
            
            testBtn.disabled = true;
            testBtn.textContent = '⏳ جاري التشغيل...';
            
            try {
                statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل اختبار النظام الحقيقي...</div>';
                
                // التحقق من وجود BugBountyCore
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore غير محمل');
                }
                
                const bugBountyCore = new BugBountyCore();
                console.log('✅ تم إنشاء BugBountyCore');
                
                // تطبيق الدوال الـ36 على جميع الثغرات
                console.log('🔥 تطبيق الدوال الـ36 على جميع الثغرات...');
                for (let i = 0; i < testVulnerabilities.length; i++) {
                    const vuln = testVulnerabilities[i];
                    const realData = realDataByType[vuln.type];
                    
                    console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
                    await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
                    console.log(`✅ تم تطبيق الدوال على: ${vuln.name}`);
                }
                
                // اختبار التقرير الرئيسي
                console.log('📋 اختبار التقرير الرئيسي...');
                const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
                
                // اختبار التقرير المنفصل
                console.log('📄 اختبار التقرير المنفصل...');
                const separateReportHTML = await bugBountyCore.formatSinglePageReport({
                    page_name: 'صفحة الاختبار الشامل',
                    page_url: 'https://example.com/comprehensive-test',
                    vulnerabilities: testVulnerabilities
                });
                
                // تحليل النتائج
                const mainAnalysis = analyzeReport(mainReportHTML, 'التقرير الرئيسي');
                const separateAnalysis = analyzeReport(separateReportHTML, 'التقرير المنفصل');
                
                // عرض النتائج
                displayResults(mainAnalysis, separateAnalysis, mainReportHTML, separateReportHTML);
                
                statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار النظام الحقيقي بنجاح!</div>';
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 تشغيل اختبار النظام الحقيقي';
            }
        }
        
        function analyzeReport(reportHTML, reportType) {
            const analysis = {
                type: reportType,
                totalSize: reportHTML.length,
                hasComprehensiveStructure: reportHTML.includes('النظام v4.0'),
                hasFunctionGroups: reportHTML.includes('المجموعة الأولى'),
                hasComprehensiveFiles: reportHTML.includes('الملفات الشاملة التفصيلية'),
                hasSystemSummary: reportHTML.includes('ملخص شامل لحالة النظام'),
                isWellFormatted: reportHTML.includes('style='),
                hasRealContent: !reportHTML.includes('لم يتم إنتاج محتوى'),
                contentQuality: 'unknown'
            };
            
            // حساب نقاط الجودة
            const qualityScore = [
                analysis.hasComprehensiveStructure,
                analysis.hasFunctionGroups,
                analysis.hasComprehensiveFiles,
                analysis.hasSystemSummary,
                analysis.isWellFormatted,
                analysis.hasRealContent
            ].filter(Boolean).length;
            
            if (qualityScore >= 5) analysis.contentQuality = 'excellent';
            else if (qualityScore >= 4) analysis.contentQuality = 'good';
            else if (qualityScore >= 2) analysis.contentQuality = 'fair';
            else analysis.contentQuality = 'poor';
            
            return analysis;
        }
        
        function displayResults(mainAnalysis, separateAnalysis, mainHTML, separateHTML) {
            // عرض المقارنة
            const comparisonDiv = document.getElementById('comparisonResults');
            const sizeDiff = Math.abs(mainAnalysis.totalSize - separateAnalysis.totalSize);
            const qualityMatch = mainAnalysis.contentQuality === separateAnalysis.contentQuality;
            
            comparisonDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${qualityMatch ? 'excellent' : 'warning'}">${qualityMatch ? '✅' : '❌'}</div>
                        <div>جودة متطابقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(sizeDiff / 1024)} KB</div>
                        <div>فرق الحجم</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure ? 'excellent' : 'poor'}">${mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure ? '✅' : '❌'}</div>
                        <div>بنية شاملة في كليهما</div>
                    </div>
                </div>
            `;
            
            // عرض تحليل التقرير الرئيسي
            displayReportAnalysis(mainAnalysis, 'mainReportResults');
            
            // عرض تحليل التقرير المنفصل
            displayReportAnalysis(separateAnalysis, 'separateReportResults');
            
            // عرض عينات المحتوى
            const samplesDiv = document.getElementById('contentSamples');
            samplesDiv.innerHTML = `
                <h4>📋 عينة من التقرير الرئيسي (أول 500 حرف):</h4>
                <div class="code">${mainHTML.substring(0, 500)}...</div>
                
                <h4>📄 عينة من التقرير المنفصل (أول 500 حرف):</h4>
                <div class="code">${separateHTML.substring(0, 500)}...</div>
            `;
        }
        
        function displayReportAnalysis(analysis, elementId) {
            const element = document.getElementById(elementId);
            
            element.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.contentQuality}">${getQualityText(analysis.contentQuality)}</div>
                        <div>جودة المحتوى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.totalSize.toLocaleString()}</div>
                        <div>حجم المحتوى (حرف)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(analysis.totalSize / 1024)} KB</div>
                        <div>حجم المحتوى (KB)</div>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveStructure ? 'excellent' : 'poor'}">${analysis.hasComprehensiveStructure ? '✅' : '❌'}</div>
                        <div>بنية شاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasFunctionGroups ? 'excellent' : 'poor'}">${analysis.hasFunctionGroups ? '✅' : '❌'}</div>
                        <div>مجموعات الدوال</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveFiles ? 'excellent' : 'poor'}">${analysis.hasComprehensiveFiles ? '✅' : '❌'}</div>
                        <div>الملفات الشاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasSystemSummary ? 'excellent' : 'poor'}">${analysis.hasSystemSummary ? '✅' : '❌'}</div>
                        <div>ملخص النظام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.isWellFormatted ? 'excellent' : 'poor'}">${analysis.isWellFormatted ? '✅' : '❌'}</div>
                        <div>تنسيق جيد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasRealContent ? 'excellent' : 'poor'}">${analysis.hasRealContent ? '✅' : '❌'}</div>
                        <div>محتوى حقيقي</div>
                    </div>
                </div>
            `;
        }
        
        function getQualityText(quality) {
            const qualityTexts = {
                'excellent': 'ممتاز',
                'good': 'جيد',
                'fair': 'مقبول',
                'poor': 'ضعيف'
            };
            return qualityTexts[quality] || quality;
        }
        
        function clearResults() {
            document.getElementById('testStatus').innerHTML = '📋 جاهز لبدء الاختبار';
            document.getElementById('comparisonResults').innerHTML = '';
            document.getElementById('mainReportResults').innerHTML = '';
            document.getElementById('separateReportResults').innerHTML = '';
            document.getElementById('contentSamples').innerHTML = '';
        }
        
        // رسالة الترحيب
        window.addEventListener('load', function() {
            console.log('🌐 صفحة اختبار النظام الحقيقي جاهزة');
            console.log('🚀 اضغط على زر "تشغيل اختبار النظام الحقيقي" لبدء الاختبار');
        });
    </script>
</body>
</html>
