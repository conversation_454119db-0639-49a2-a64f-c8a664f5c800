/**
 * اختبار شامل للتقارير (الرئيسي والمنفصل) 
 * للتحقق من عرض المحتوى بشكل صحيح ومتناسق وليس متداخل
 * مع استخدام الدوال الـ36 والملفات الشاملة التفصيلية
 */

console.log('🔥 بدء اختبار التقارير الشامل...');

// محاكاة ثغرات حقيقية للاختبار
const testVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        location: 'Login Form',
        method: 'POST'
    },
    {
        name: 'Cross-Site Scripting في حقل البحث',
        type: 'XSS',
        severity: 'High', 
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>',
        location: 'Search Form',
        method: 'GET'
    },
    {
        name: 'CSRF في تغيير كلمة المرور',
        type: 'CSRF',
        severity: 'Medium',
        url: 'https://example.com/change-password.php',
        parameter: 'new_password',
        payload: '<form action="..." method="post">',
        location: 'Password Change Form',
        method: 'POST'
    }
];

// بيانات حقيقية مفصلة لكل ثغرة
const realDataForVulns = {
    'SQL Injection': {
        payload_used: "admin' OR '1'='1' --",
        response_received: 'MySQL Error: You have an error in your SQL syntax near OR at line 1',
        impact_observed: 'تم تجاوز المصادقة بنجاح والوصول لحساب المدير',
        evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL والجداول',
        injection_point: 'username parameter في POST request',
        exploitation_result: 'تم الدخول كمدير بدون كلمة مرور صحيحة',
        database_info: 'MySQL 5.7.33, database: webapp_db, table: users',
        extracted_data: 'usernames, password hashes, email addresses'
    },
    'XSS': {
        payload_used: '<script>alert("XSS Vulnerability Found")</script>',
        response_received: 'تم عرض الكود JavaScript في الصفحة بدون تنظيف',
        impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
        evidence_found: 'ظهور نافذة alert تؤكد تنفيذ الكود',
        injection_point: 'query parameter في GET request',
        exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
        browser_info: 'Chrome 91.0, Firefox 89.0 - vulnerable in both',
        session_impact: 'يمكن سرقة session tokens والتحكم في الحساب'
    },
    'CSRF': {
        payload_used: '<form action="https://example.com/change-password.php" method="post"><input name="new_password" value="hacked123"></form>',
        response_received: 'تم تغيير كلمة المرور بنجاح بدون تحقق من CSRF token',
        impact_observed: 'تغيير كلمة مرور المستخدم من موقع خارجي',
        evidence_found: 'عدم وجود CSRF protection في النموذج',
        injection_point: 'change-password form عبر external website',
        exploitation_result: 'تم تغيير كلمة المرور إلى "hacked123"',
        attack_vector: 'malicious website hosting the CSRF form',
        protection_missing: 'no CSRF tokens, no referrer validation'
    }
};

async function testComprehensiveReports() {
    console.log('🧪 بدء اختبار التقارير الشامل...');
    
    try {
        // التحقق من وجود BugBountyCore
        if (typeof BugBountyCore === 'undefined') {
            throw new Error('BugBountyCore غير محمل');
        }
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // تطبيق الدوال الـ36 على كل ثغرة
        console.log('🔥 تطبيق الدوال الـ36 على جميع الثغرات...');
        
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            const realData = realDataForVulns[vuln.type];
            
            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            // تطبيق جميع الدوال الـ36 والملفات الشاملة
            await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
            
            console.log(`✅ تم تطبيق الدوال على الثغرة: ${vuln.name}`);
            console.log(`📊 comprehensive_details: ${vuln.comprehensive_details ? vuln.comprehensive_details.length + ' حرف' : 'غير موجود'}`);
            console.log(`📊 comprehensive_analysis: ${vuln.comprehensive_analysis ? vuln.comprehensive_analysis.length + ' حرف' : 'غير موجود'}`);
            console.log(`📊 security_impact_analysis: ${vuln.security_impact_analysis ? vuln.security_impact_analysis.length + ' حرف' : 'غير موجود'}`);
        }
        
        // اختبار التقرير الرئيسي
        console.log('📋 اختبار التقرير الرئيسي...');
        const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
        
        // تحليل التقرير الرئيسي
        const mainReportAnalysis = analyzeReportContent(mainReportHTML, 'التقرير الرئيسي');
        console.log('📊 تحليل التقرير الرئيسي:', mainReportAnalysis);
        
        // اختبار التقرير المنفصل
        console.log('📄 اختبار التقرير المنفصل...');
        const separateReportHTML = await bugBountyCore.formatSinglePageReport({
            page_name: 'صفحة الاختبار',
            page_url: 'https://example.com/test',
            vulnerabilities: testVulnerabilities
        });
        
        // تحليل التقرير المنفصل
        const separateReportAnalysis = analyzeReportContent(separateReportHTML, 'التقرير المنفصل');
        console.log('📊 تحليل التقرير المنفصل:', separateReportAnalysis);
        
        // مقارنة التقارير
        const comparison = compareReports(mainReportAnalysis, separateReportAnalysis);
        console.log('🔍 مقارنة التقارير:', comparison);
        
        // النتيجة النهائية
        const finalResult = {
            success: true,
            mainReport: {
                size: mainReportHTML.length,
                analysis: mainReportAnalysis,
                sample: mainReportHTML.substring(0, 500) + '...'
            },
            separateReport: {
                size: separateReportHTML.length,
                analysis: separateReportAnalysis,
                sample: separateReportHTML.substring(0, 500) + '...'
            },
            comparison: comparison,
            vulnerabilities_processed: testVulnerabilities.length,
            functions_applied: 36,
            timestamp: new Date().toISOString()
        };
        
        console.log('🎉 اكتمل اختبار التقارير الشامل!');
        return finalResult;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التقارير:', error);
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// دالة تحليل محتوى التقرير
function analyzeReportContent(reportHTML, reportType) {
    const analysis = {
        type: reportType,
        totalSize: reportHTML.length,
        hasComprehensiveContent: false,
        hasGenericMessages: false,
        hasRealData: false,
        isWellFormatted: false,
        contentQuality: 'unknown',
        issues: []
    };
    
    // فحص المحتوى الشامل
    const comprehensiveIndicators = [
        'comprehensive_details',
        'comprehensive_analysis', 
        'security_impact_analysis',
        'generateComprehensiveDetailsFromRealData',
        'generateComprehensiveVulnerabilityAnalysis'
    ];
    
    let comprehensiveCount = 0;
    comprehensiveIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            comprehensiveCount++;
        }
    });
    
    analysis.hasComprehensiveContent = comprehensiveCount >= 3;
    
    // فحص الرسائل العامة
    const genericMessages = [
        'تم تطبيق الدالة بنجاح',
        'تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح'
    ];
    
    genericMessages.forEach(msg => {
        if (reportHTML.includes(msg)) {
            analysis.hasGenericMessages = true;
            analysis.issues.push(`يحتوي على رسالة عامة: ${msg}`);
        }
    });
    
    // فحص البيانات الحقيقية
    const realDataIndicators = [
        'MySQL Error',
        'alert("XSS")',
        'CSRF token',
        'payload_used',
        'exploitation_result'
    ];
    
    let realDataCount = 0;
    realDataIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            realDataCount++;
        }
    });
    
    analysis.hasRealData = realDataCount >= 2;
    
    // فحص التنسيق
    const formatIndicators = [
        '<div class="vulnerability-item',
        '<div class="comprehensive-details',
        'vulnerability-header',
        'severity-badge'
    ];
    
    let formatCount = 0;
    formatIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            formatCount++;
        }
    });
    
    analysis.isWellFormatted = formatCount >= 3;
    
    // تقييم جودة المحتوى
    if (analysis.hasComprehensiveContent && analysis.hasRealData && !analysis.hasGenericMessages && analysis.isWellFormatted) {
        analysis.contentQuality = 'excellent';
    } else if (analysis.hasComprehensiveContent && analysis.hasRealData && analysis.isWellFormatted) {
        analysis.contentQuality = 'good';
    } else if (analysis.hasComprehensiveContent || analysis.hasRealData) {
        analysis.contentQuality = 'fair';
    } else {
        analysis.contentQuality = 'poor';
        analysis.issues.push('محتوى غير كافي أو عام');
    }
    
    // فحص التداخل
    const nestedDivCount = (reportHTML.match(/<div[^>]*>/g) || []).length;
    const closingDivCount = (reportHTML.match(/<\/div>/g) || []).length;
    
    if (nestedDivCount !== closingDivCount) {
        analysis.issues.push('عدم توازن في عناصر div - قد يكون هناك تداخل');
    }
    
    return analysis;
}

// دالة مقارنة التقارير
function compareReports(mainAnalysis, separateAnalysis) {
    return {
        sizeDifference: Math.abs(mainAnalysis.totalSize - separateAnalysis.totalSize),
        qualityMatch: mainAnalysis.contentQuality === separateAnalysis.contentQuality,
        bothHaveComprehensiveContent: mainAnalysis.hasComprehensiveContent && separateAnalysis.hasComprehensiveContent,
        bothHaveRealData: mainAnalysis.hasRealData && separateAnalysis.hasRealData,
        bothWellFormatted: mainAnalysis.isWellFormatted && separateAnalysis.isWellFormatted,
        issuesComparison: {
            main: mainAnalysis.issues.length,
            separate: separateAnalysis.issues.length
        },
        recommendation: generateRecommendation(mainAnalysis, separateAnalysis)
    };
}

// دالة إنشاء التوصيات
function generateRecommendation(mainAnalysis, separateAnalysis) {
    const recommendations = [];
    
    if (!mainAnalysis.hasComprehensiveContent || !separateAnalysis.hasComprehensiveContent) {
        recommendations.push('تحسين المحتوى الشامل في التقارير');
    }
    
    if (mainAnalysis.hasGenericMessages || separateAnalysis.hasGenericMessages) {
        recommendations.push('إزالة الرسائل العامة واستبدالها بمحتوى حقيقي');
    }
    
    if (!mainAnalysis.hasRealData || !separateAnalysis.hasRealData) {
        recommendations.push('إضافة المزيد من البيانات الحقيقية المستخرجة من الثغرات');
    }
    
    if (!mainAnalysis.isWellFormatted || !separateAnalysis.isWellFormatted) {
        recommendations.push('تحسين تنسيق وعرض التقارير');
    }
    
    if (recommendations.length === 0) {
        recommendations.push('التقارير تبدو جيدة - استمر في التطوير');
    }
    
    return recommendations;
}

// تصدير الدالة للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testComprehensiveReports };
} else if (typeof window !== 'undefined') {
    window.testComprehensiveReports = testComprehensiveReports;
}

console.log('📋 ملف اختبار التقارير الشامل جاهز للتشغيل');
console.log('🚀 استخدم: testComprehensiveReports() لبدء الاختبار');
