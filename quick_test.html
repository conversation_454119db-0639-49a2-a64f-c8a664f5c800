<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للدوال الـ36</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .result { background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔥 اختبار سريع للدوال الـ36 الشاملة التفصيلية</h1>
    
    <button onclick="runQuickTest()">🚀 تشغيل الاختبار</button>
    <div id="results"></div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    
    <script>
        async function runQuickTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">⏳ جاري تشغيل الاختبار...</div>';
            
            try {
                console.log('🔥 بدء الاختبار السريع...');
                
                // التحقق من وجود BugBountyCore
                if (typeof BugBountyCore === 'undefined') {
                    resultsDiv.innerHTML = '<div class="error">❌ BugBountyCore غير محمل</div>';
                    return;
                }
                
                // إنشاء instance
                const bugBountyCore = new BugBountyCore();
                console.log('✅ تم إنشاء BugBountyCore');
                
                // ثغرة تجريبية
                const testVuln = {
                    name: 'SQL Injection Test',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://example.com/login',
                    parameter: 'username'
                };
                
                const testRealData = {
                    payload_used: "' OR 1=1 --",
                    response_received: 'Database error revealed',
                    impact_observed: 'Authentication bypass',
                    evidence_found: 'Error messages'
                };
                
                let results = '<h2>📊 نتائج الاختبار:</h2>';
                
                // اختبار الدالة الأولى
                console.log('🔍 اختبار generateComprehensiveDetailsFromRealData...');
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                
                results += '<h3>🔍 generateComprehensiveDetailsFromRealData:</h3>';
                results += '<div class="result">النوع: ' + typeof comprehensiveDetails + '</div>';
                results += '<div class="result">الطول: ' + (comprehensiveDetails ? comprehensiveDetails.length : 'undefined') + '</div>';
                if (comprehensiveDetails) {
                    results += '<div class="code">' + comprehensiveDetails.substring(0, 300) + '...</div>';
                } else {
                    results += '<div class="error">❌ لا يوجد محتوى</div>';
                }
                
                // اختبار الدالة الثانية
                console.log('🔍 اختبار generateComprehensiveVulnerabilityAnalysis...');
                const comprehensiveAnalysis = await bugBountyCore.generateComprehensiveVulnerabilityAnalysis(testVuln, testRealData);
                
                results += '<h3>🔬 generateComprehensiveVulnerabilityAnalysis:</h3>';
                results += '<div class="result">النوع: ' + typeof comprehensiveAnalysis + '</div>';
                results += '<div class="result">الطول: ' + (comprehensiveAnalysis ? comprehensiveAnalysis.length : 'undefined') + '</div>';
                if (comprehensiveAnalysis) {
                    results += '<div class="code">' + comprehensiveAnalysis.substring(0, 300) + '...</div>';
                } else {
                    results += '<div class="error">❌ لا يوجد محتوى</div>';
                }
                
                // اختبار الدالة الثالثة
                console.log('🔍 اختبار generateDynamicSecurityImpactAnalysis...');
                const securityImpact = await bugBountyCore.generateDynamicSecurityImpactAnalysis(testVuln, testRealData);
                
                results += '<h3>🛡️ generateDynamicSecurityImpactAnalysis:</h3>';
                results += '<div class="result">النوع: ' + typeof securityImpact + '</div>';
                results += '<div class="result">الطول: ' + (securityImpact ? securityImpact.length : 'undefined') + '</div>';
                if (securityImpact) {
                    results += '<div class="code">' + securityImpact.substring(0, 300) + '...</div>';
                } else {
                    results += '<div class="error">❌ لا يوجد محتوى</div>';
                }
                
                // اختبار تطبيق الدوال على كائن الثغرة
                console.log('🔧 اختبار تطبيق الدوال على كائن الثغرة...');
                testVuln.comprehensive_details = comprehensiveDetails;
                testVuln.comprehensive_analysis = comprehensiveAnalysis;
                testVuln.security_impact_analysis = securityImpact;
                
                results += '<h3>🔧 اختبار العرض في HTML:</h3>';
                const htmlDisplay1 = testVuln.comprehensive_details || 'تم تطبيق الدالة بنجاح';
                const htmlDisplay2 = testVuln.comprehensive_analysis || 'تم التحليل الشامل للثغرة';
                const htmlDisplay3 = testVuln.security_impact_analysis || 'تم تحليل التأثير الأمني';
                
                results += '<div class="result">HTML Display 1: ' + htmlDisplay1.substring(0, 100) + '...</div>';
                results += '<div class="result">HTML Display 2: ' + htmlDisplay2.substring(0, 100) + '...</div>';
                results += '<div class="result">HTML Display 3: ' + htmlDisplay3.substring(0, 100) + '...</div>';
                
                // تحليل المشكلة
                results += '<h3>🔍 تحليل المشكلة:</h3>';
                if (comprehensiveDetails && comprehensiveDetails.length > 50) {
                    results += '<div class="result">✅ الدوال تعمل بشكل صحيح وتُنتج محتوى شامل</div>';
                    results += '<div class="result">❌ المشكلة في العرض أو في حفظ النتائج في التقارير الفعلية</div>';
                } else {
                    results += '<div class="error">❌ الدوال لا تُنتج محتوى شامل</div>';
                    results += '<div class="error">🔧 يجب إصلاح الدوال نفسها</div>';
                }
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="error">❌ خطأ في الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
    </script>
</body>
</html>
