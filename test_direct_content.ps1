# Direct Content Test - Test the actual system through localhost:3000
Write-Host "Starting DIRECT content test through localhost:3000..." -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Test Results
$testResults = @{
    SystemAccessible = $false
    MainPageLoaded = $false
    ComprehensiveContentFound = 0
    RealDataDisplayFound = 0
    FunctionsContentFound = 0
    FilesContentFound = 0
    TotalScore = 0
}

# Test if localhost:3000 is accessible
Write-Host "`nTesting localhost:3000 accessibility..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction Stop
    $testResults.SystemAccessible = $true
    $testResults.MainPageLoaded = $true
    Write-Host "System is accessible on localhost:3000" -ForegroundColor Green
    Write-Host "Response size: $($response.Content.Length) characters" -ForegroundColor White
    
    # Check main page content
    $mainContent = $response.Content
    
    # Check for comprehensive functions in the main page
    Write-Host "`nChecking for comprehensive functions in main page..." -ForegroundColor Yellow
    
    $comprehensiveFunctions = @(
        "generateComprehensiveDetailsFromRealData",
        "extractRealDataFromDiscoveredVulnerability",
        "generateDynamicImpactForAnyVulnerability", 
        "generateRealExploitationStepsForVulnerabilityComprehensive",
        "generateDynamicRecommendationsForVulnerability",
        "generateComprehensiveVulnerabilityAnalysis",
        "generateDynamicSecurityImpactAnalysis",
        "generateRealTimeVulnerabilityAssessment",
        "generateComprehensiveRiskAnalysis",
        "generateDynamicThreatModelingForVulnerability"
    )
    
    $foundFunctions = 0
    foreach ($func in $comprehensiveFunctions) {
        if ($mainContent -match [regex]::Escape($func)) {
            $foundFunctions++
            Write-Host "   Found function: $func" -ForegroundColor Green
        }
    }
    
    $testResults.FunctionsContentFound = $foundFunctions
    Write-Host "   Functions found: $foundFunctions/$($comprehensiveFunctions.Count)" -ForegroundColor White
    
    # Check for real data display modifications
    Write-Host "`nChecking for real data display modifications..." -ForegroundColor Yellow
    
    $realDataModifications = @(
        "max-height: 200px; overflow-y: auto",
        "JSON.stringify",
        "typeof.*object",
        "comprehensive_details",
        "dynamic_impact",
        "exploitation_steps",
        "visual_impact_data"
    )
    
    $foundModifications = 0
    foreach ($mod in $realDataModifications) {
        if ($mainContent -match $mod) {
            $foundModifications++
            Write-Host "   Found modification: $mod" -ForegroundColor Green
        }
    }
    
    $testResults.RealDataDisplayFound = $foundModifications
    Write-Host "   Modifications found: $foundModifications/$($realDataModifications.Count)" -ForegroundColor White
    
    # Check for comprehensive content indicators
    Write-Host "`nChecking for comprehensive content indicators..." -ForegroundColor Yellow
    
    $comprehensiveContent = @(
        "comprehensive_description",
        "technical_details",
        "exploitation_results", 
        "impact_analysis",
        "security_implications",
        "business_impact",
        "vulnerability_assessment"
    )
    
    $foundContent = 0
    foreach ($content in $comprehensiveContent) {
        if ($mainContent -match [regex]::Escape($content)) {
            $foundContent++
            Write-Host "   Found content: $content" -ForegroundColor Green
        }
    }
    
    $testResults.ComprehensiveContentFound = $foundContent
    Write-Host "   Content indicators found: $foundContent/$($comprehensiveContent.Count)" -ForegroundColor White
    
    # Check for system files integration
    Write-Host "`nChecking for system files integration..." -ForegroundColor Yellow
    
    $systemFilesIndicators = @(
        "impact_visualizer.js",
        "textual_impact_analyzer.js", 
        "ImpactVisualizer",
        "TextualImpactAnalyzer",
        "createVulnerabilityVisualization",
        "analyzeVulnerabilityImpact"
    )
    
    $foundFiles = 0
    foreach ($file in $systemFilesIndicators) {
        if ($mainContent -match [regex]::Escape($file)) {
            $foundFiles++
            Write-Host "   Found file integration: $file" -ForegroundColor Green
        }
    }
    
    $testResults.FilesContentFound = $foundFiles
    Write-Host "   File integrations found: $foundFiles/$($systemFilesIndicators.Count)" -ForegroundColor White
    
} catch {
    Write-Host "System is not accessible on localhost:3000" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure the system is running on localhost:3000" -ForegroundColor Yellow
}

# Test BugBountyCore.js file directly
Write-Host "`nTesting BugBountyCore.js file directly..." -ForegroundColor Yellow

$coreFile = "assets\modules\bugbounty\BugBountyCore.js"
if (Test-Path $coreFile) {
    $coreContent = Get-Content $coreFile -Raw -Encoding UTF8
    Write-Host "BugBountyCore.js file found - size: $($coreContent.Length) characters" -ForegroundColor Green
    
    # Check for comprehensive function implementations
    Write-Host "`nChecking comprehensive function implementations..." -ForegroundColor Yellow
    
    $functionImplementations = @(
        "generateComprehensiveDetailsFromRealData.*{",
        "extractRealDataFromDiscoveredVulnerability.*{",
        "generateDynamicImpactForAnyVulnerability.*{",
        "comprehensive_description.*:",
        "technical_details.*:",
        "exploitation_results.*:"
    )
    
    $foundImplementations = 0
    foreach ($impl in $functionImplementations) {
        if ($coreContent -match $impl) {
            $foundImplementations++
            Write-Host "   Found implementation: $impl" -ForegroundColor Green
        }
    }
    
    Write-Host "   Function implementations found: $foundImplementations/$($functionImplementations.Count)" -ForegroundColor White
    
    # Check for real content generation
    Write-Host "`nChecking for real content generation patterns..." -ForegroundColor Yellow
    
    $contentPatterns = @(
        "comprehensive.*analysis",
        "detailed.*assessment",
        "technical.*details",
        "exploitation.*results",
        "impact.*analysis",
        "security.*implications",
        "vulnerability.*assessment"
    )
    
    $foundPatterns = 0
    foreach ($pattern in $contentPatterns) {
        if ($coreContent -match $pattern) {
            $foundPatterns++
            Write-Host "   Found content pattern: $pattern" -ForegroundColor Green
        }
    }
    
    Write-Host "   Content patterns found: $foundPatterns/$($contentPatterns.Count)" -ForegroundColor White
    
    # Check for template usage
    Write-Host "`nChecking for comprehensive template usage..." -ForegroundColor Yellow
    
    $templateElements = @(
        "vulnerability-item",
        "vulnerability-header",
        "vulnerability-title", 
        "severity-badge",
        "vulnerability-details",
        "detail-item",
        "detail-label",
        "detail-value"
    )
    
    $foundTemplateElements = 0
    foreach ($element in $templateElements) {
        if ($coreContent -match [regex]::Escape($element)) {
            $foundTemplateElements++
            Write-Host "   Found template element: $element" -ForegroundColor Green
        }
    }
    
    Write-Host "   Template elements found: $foundTemplateElements/$($templateElements.Count)" -ForegroundColor White
    
} else {
    Write-Host "BugBountyCore.js file not found" -ForegroundColor Red
}

# Calculate final score
Write-Host "`nCalculating final score..." -ForegroundColor Yellow

$maxFunctions = 10
$maxModifications = 7
$maxContent = 7
$maxFiles = 6

$functionsScore = [math]::Min(($testResults.FunctionsContentFound / $maxFunctions) * 30, 30)
$modificationsScore = [math]::Min(($testResults.RealDataDisplayFound / $maxModifications) * 25, 25)
$contentScore = [math]::Min(($testResults.ComprehensiveContentFound / $maxContent) * 25, 25)
$filesScore = [math]::Min(($testResults.FilesContentFound / $maxFiles) * 20, 20)

$testResults.TotalScore = $functionsScore + $modificationsScore + $contentScore + $filesScore

# Display final results
Write-Host "`nFINAL DIRECT CONTENT TEST RESULTS" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

Write-Host "System Status:" -ForegroundColor White
Write-Host "   System Accessible: $($testResults.SystemAccessible)" -ForegroundColor White
Write-Host "   Main Page Loaded: $($testResults.MainPageLoaded)" -ForegroundColor White

Write-Host "`nContent Analysis:" -ForegroundColor White
Write-Host "   Functions Found: $($testResults.FunctionsContentFound)/$maxFunctions ($([math]::Round($functionsScore, 2))/30 points)" -ForegroundColor White
Write-Host "   Modifications Found: $($testResults.RealDataDisplayFound)/$maxModifications ($([math]::Round($modificationsScore, 2))/25 points)" -ForegroundColor White
Write-Host "   Comprehensive Content: $($testResults.ComprehensiveContentFound)/$maxContent ($([math]::Round($contentScore, 2))/25 points)" -ForegroundColor White
Write-Host "   Files Integration: $($testResults.FilesContentFound)/$maxFiles ($([math]::Round($filesScore, 2))/20 points)" -ForegroundColor White

Write-Host "`nTotal Score: $([math]::Round($testResults.TotalScore, 2))/100" -ForegroundColor Yellow

if ($testResults.TotalScore -ge 90) {
    Write-Host "EXCELLENT! Comprehensive content system is fully implemented!" -ForegroundColor Green
} elseif ($testResults.TotalScore -ge 70) {
    Write-Host "GOOD! Most comprehensive content features are implemented" -ForegroundColor Yellow
} elseif ($testResults.TotalScore -ge 50) {
    Write-Host "PARTIAL! Some comprehensive content features are implemented" -ForegroundColor Yellow
} else {
    Write-Host "NEEDS WORK! Comprehensive content system needs implementation" -ForegroundColor Red
}

# Provide specific recommendations
Write-Host "`nRecommendations:" -ForegroundColor Cyan

if ($testResults.FunctionsContentFound -lt 8) {
    Write-Host "   - Implement more comprehensive functions" -ForegroundColor Yellow
}

if ($testResults.RealDataDisplayFound -lt 5) {
    Write-Host "   - Add real data display modifications" -ForegroundColor Yellow
}

if ($testResults.ComprehensiveContentFound -lt 5) {
    Write-Host "   - Add comprehensive content indicators" -ForegroundColor Yellow
}

if ($testResults.FilesContentFound -lt 4) {
    Write-Host "   - Integrate system files properly" -ForegroundColor Yellow
}

if ($testResults.TotalScore -ge 90) {
    Write-Host "   - System is ready for production use!" -ForegroundColor Green
}

# Save detailed results
$reportContent = @"
# Direct Content Test Results
Test Date: $timestamp

## System Status:
- System Accessible: $($testResults.SystemAccessible)
- Main Page Loaded: $($testResults.MainPageLoaded)

## Content Analysis:
- Functions Found: $($testResults.FunctionsContentFound)/$maxFunctions
- Modifications Found: $($testResults.RealDataDisplayFound)/$maxModifications
- Comprehensive Content: $($testResults.ComprehensiveContentFound)/$maxContent
- Files Integration: $($testResults.FilesContentFound)/$maxFiles

## Total Score: $([math]::Round($testResults.TotalScore, 2))/100

## Assessment:
$(if ($testResults.TotalScore -ge 90) { "EXCELLENT! Comprehensive content system is fully implemented!" } 
  elseif ($testResults.TotalScore -ge 70) { "GOOD! Most comprehensive content features are implemented" } 
  elseif ($testResults.TotalScore -ge 50) { "PARTIAL! Some comprehensive content features are implemented" }
  else { "NEEDS WORK! Comprehensive content system needs implementation" })

## Key Findings:
- System accessibility: $($testResults.SystemAccessible)
- Functions implementation: $($testResults.FunctionsContentFound -ge 8)
- Real data display: $($testResults.RealDataDisplayFound -ge 5)
- Comprehensive content: $($testResults.ComprehensiveContentFound -ge 5)
- Files integration: $($testResults.FilesContentFound -ge 4)
"@

$reportContent | Out-File -FilePath "direct_content_test_report.md" -Encoding UTF8
Write-Host "`nDetailed test report saved to: direct_content_test_report.md" -ForegroundColor Green

Write-Host "`nDirect content test completed" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
