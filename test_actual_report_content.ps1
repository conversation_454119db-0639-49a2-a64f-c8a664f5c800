# Test ACTUAL Report Content - Generate real reports and check comprehensive content
Write-Host "Testing ACTUAL REPORT CONTENT - generating real reports..." -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Test Results
$testResults = @{
    MainReportGenerated = $false
    SeparateReportGenerated = $false
    ComprehensiveContentInMain = 0
    ComprehensiveContentInSeparate = 0
    RealDataInMain = 0
    RealDataInSeparate = 0
    Functions36InMain = 0
    Functions36InSeparate = 0
    FilesContentInMain = 0
    FilesContentInSeparate = 0
    TotalScore = 0
}

# Create test HTML to generate actual reports
Write-Host "`nCreating test HTML to generate actual reports..." -ForegroundColor Yellow

$testHTML = @"
<!DOCTYPE html>
<html>
<head>
    <title>Actual Report Content Test</title>
    <script src="assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
</head>
<body>
    <div id="results"></div>
    <script>
        async function generateAndTestReports() {
            try {
                console.log('Starting actual report generation test...');
                
                // Create BugBountyCore instance
                const bugBountyCore = new BugBountyCore();
                
                // Create test vulnerability with comprehensive data
                const testVuln = {
                    name: 'API Authentication Bypass',
                    type: 'Authentication Bypass',
                    severity: 'Medium',
                    url: 'http://testphp.vulnweb.com',
                    parameter: 'auth_token',
                    description: 'Authentication bypass vulnerability discovered during testing',
                    payload: 'admin\\'||1=1--',
                    evidence: 'Server response indicates successful authentication bypass'
                };
                
                console.log('Test vulnerability created:', testVuln);
                
                // Apply comprehensive functions to generate real data
                console.log('Applying comprehensive functions...');
                
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability ? 
                    bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln) : {};
                
                if (bugBountyCore.generateComprehensiveDetailsFromRealData) {
                    testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                    console.log('comprehensive_details generated:', testVuln.comprehensive_details);
                }
                
                if (bugBountyCore.generateDynamicImpactForAnyVulnerability) {
                    testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
                    console.log('dynamic_impact generated:', testVuln.dynamic_impact);
                }
                
                if (bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive) {
                    testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
                    console.log('exploitation_steps generated:', testVuln.exploitation_steps);
                }
                
                if (bugBountyCore.generateDynamicRecommendationsForVulnerability) {
                    testVuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln, realData);
                    console.log('dynamic_recommendations generated:', testVuln.dynamic_recommendations);
                }
                
                // Apply system files
                if (typeof ImpactVisualizer !== 'undefined') {
                    const impactVisualizer = new ImpactVisualizer(bugBountyCore);
                    testVuln.visual_impact_data = await impactVisualizer.createVulnerabilityVisualization(testVuln, realData);
                    console.log('visual_impact_data generated:', testVuln.visual_impact_data);
                }
                
                if (typeof TextualImpactAnalyzer !== 'undefined') {
                    const textualAnalyzer = new TextualImpactAnalyzer();
                    testVuln.textual_impact_analysis = await textualAnalyzer.analyzeVulnerabilityImpact(testVuln, realData);
                    console.log('textual_impact_analysis generated:', testVuln.textual_impact_analysis);
                }
                
                // Generate MAIN REPORT
                console.log('Generating MAIN REPORT...');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                console.log('Main report generated - size:', mainReport ? mainReport.length : 0);
                
                // Generate SEPARATE REPORT
                console.log('Generating SEPARATE REPORT...');
                const pageData = {
                    page_name: 'Test Page',
                    page_url: 'http://testphp.vulnweb.com',
                    vulnerabilities: [testVuln]
                };
                const separateReport = await bugBountyCore.formatSinglePageReport(pageData);
                console.log('Separate report generated - size:', separateReport ? separateReport.length : 0);
                
                // Save reports to window for PowerShell to access
                window.testReports = {
                    mainReport: mainReport,
                    separateReport: separateReport,
                    testVuln: testVuln,
                    realData: realData,
                    timestamp: new Date().toISOString()
                };
                
                // Display in page for verification
                document.getElementById('results').innerHTML = 
                    '<h3>Reports Generated Successfully</h3>' +
                    '<p>Main Report Size: ' + (mainReport ? mainReport.length : 0) + ' characters</p>' +
                    '<p>Separate Report Size: ' + (separateReport ? separateReport.length : 0) + ' characters</p>' +
                    '<p>Check console for details</p>';
                
                console.log('Reports saved to window.testReports for PowerShell analysis');
                
                return {
                    success: true,
                    mainReportSize: mainReport ? mainReport.length : 0,
                    separateReportSize: separateReport ? separateReport.length : 0
                };
                
            } catch (error) {
                console.error('Error generating reports:', error);
                document.getElementById('results').innerHTML = '<h3>Error: ' + error.message + '</h3>';
                return { success: false, error: error.message };
            }
        }
        
        // Generate reports immediately when page loads
        generateAndTestReports().then(result => {
            console.log('Report generation completed:', result);
        });
    </script>
</body>
</html>
"@

# Save test HTML file
$testFile = "test_actual_reports.html"
$testHTML | Out-File -FilePath $testFile -Encoding UTF8
Write-Host "Test HTML file created: $testFile" -ForegroundColor Green

# Start Python server
Write-Host "`nStarting Python server..." -ForegroundColor Yellow
$serverProcess = Start-Process python -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
Start-Sleep -Seconds 3
Write-Host "Python server started on port 3000" -ForegroundColor Green

try {
    # Open test page and wait for report generation
    Write-Host "`nOpening test page and generating reports..." -ForegroundColor Yellow
    Start-Process "http://localhost:3000/$testFile"
    
    Write-Host "Waiting for report generation (30 seconds)..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30
    
    # Try to access the generated reports through JavaScript execution
    Write-Host "`nAttempting to extract generated reports..." -ForegroundColor Yellow
    
    # Look for any saved report files in the directory
    $reportFiles = Get-ChildItem -Path "." -Filter "*Bug_Bounty*" -File | Where-Object { $_.LastWriteTime -gt (Get-Date).AddMinutes(-5) }
    
    if ($reportFiles.Count -gt 0) {
        Write-Host "Found $($reportFiles.Count) recently generated report files:" -ForegroundColor Green
        
        foreach ($file in $reportFiles) {
            Write-Host "`nAnalyzing report: $($file.Name)" -ForegroundColor Cyan
            Write-Host "File size: $($file.Length) bytes" -ForegroundColor White
            
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            
            # Determine if this is main or separate report
            $isMainReport = $file.Name -match "main|comprehensive" -or $file.Length -gt 10000
            $reportType = if ($isMainReport) { "MAIN" } else { "SEPARATE" }
            
            Write-Host "Report type: $reportType" -ForegroundColor Yellow
            
            # Check for comprehensive content indicators
            $comprehensiveIndicators = @(
                "comprehensive_description",
                "technical_details",
                "exploitation_results",
                "impact_analysis",
                "security_implications",
                "business_impact",
                "detailed_analysis"
            )
            
            $foundComprehensive = 0
            foreach ($indicator in $comprehensiveIndicators) {
                if ($content -match [regex]::Escape($indicator)) {
                    $foundComprehensive++
                    Write-Host "   ✅ Found comprehensive content: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for real data display
            $realDataIndicators = @(
                "max-height: 200px; overflow-y: auto",
                "JSON.stringify",
                "typeof.*object",
                "comprehensive_details",
                "dynamic_impact"
            )
            
            $foundRealData = 0
            foreach ($indicator in $realDataIndicators) {
                if ($content -match $indicator) {
                    $foundRealData++
                    Write-Host "   ✅ Found real data display: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for 36 functions content
            $functionsIndicators = @(
                "generateComprehensiveDetailsFromRealData",
                "generateDynamicImpactForAnyVulnerability",
                "generateRealExploitationStepsForVulnerabilityComprehensive",
                "generateDynamicRecommendationsForVulnerability",
                "extractRealDataFromDiscoveredVulnerability"
            )
            
            $foundFunctions = 0
            foreach ($indicator in $functionsIndicators) {
                if ($content -match [regex]::Escape($indicator)) {
                    $foundFunctions++
                    Write-Host "   ✅ Found function: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for system files content
            $filesIndicators = @(
                "impact_visualizer.js",
                "textual_impact_analyzer.js",
                "visual_impact_data",
                "textual_impact_analysis"
            )
            
            $foundFiles = 0
            foreach ($indicator in $filesIndicators) {
                if ($content -match [regex]::Escape($indicator)) {
                    $foundFiles++
                    Write-Host "   ✅ Found system file content: $indicator" -ForegroundColor Green
                }
            }
            
            Write-Host "   📊 Content Analysis for $reportType report:" -ForegroundColor White
            Write-Host "      Comprehensive content: $foundComprehensive/$($comprehensiveIndicators.Count)" -ForegroundColor White
            Write-Host "      Real data display: $foundRealData/$($realDataIndicators.Count)" -ForegroundColor White
            Write-Host "      Functions content: $foundFunctions/$($functionsIndicators.Count)" -ForegroundColor White
            Write-Host "      Files content: $foundFiles/$($filesIndicators.Count)" -ForegroundColor White
            
            # Update test results
            if ($isMainReport) {
                $testResults.MainReportGenerated = $true
                $testResults.ComprehensiveContentInMain = $foundComprehensive
                $testResults.RealDataInMain = $foundRealData
                $testResults.Functions36InMain = $foundFunctions
                $testResults.FilesContentInMain = $foundFiles
            } else {
                $testResults.SeparateReportGenerated = $true
                $testResults.ComprehensiveContentInSeparate = $foundComprehensive
                $testResults.RealDataInSeparate = $foundRealData
                $testResults.Functions36InSeparate = $foundFunctions
                $testResults.FilesContentInSeparate = $foundFiles
            }
        }
        
    } else {
        Write-Host "No recent report files found - reports may not have been saved to disk" -ForegroundColor Yellow
        Write-Host "This could mean reports are generated but not automatically saved" -ForegroundColor Yellow
    }
    
} finally {
    # Stop Python server
    Write-Host "`nStopping Python server..." -ForegroundColor Yellow
    Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Python server stopped" -ForegroundColor Green
    
    # Clean up test file
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "Test file cleaned up" -ForegroundColor Gray
    }
}

# Calculate final score
Write-Host "`nCalculating final score..." -ForegroundColor Yellow

$maxComprehensive = 7
$maxRealData = 5
$maxFunctions = 5
$maxFiles = 4

# Main report scoring
$mainComprehensiveScore = [math]::Min(($testResults.ComprehensiveContentInMain / $maxComprehensive) * 20, 20)
$mainRealDataScore = [math]::Min(($testResults.RealDataInMain / $maxRealData) * 15, 15)
$mainFunctionsScore = [math]::Min(($testResults.Functions36InMain / $maxFunctions) * 10, 10)
$mainFilesScore = [math]::Min(($testResults.FilesContentInMain / $maxFiles) * 5, 5)

# Separate report scoring
$separateComprehensiveScore = [math]::Min(($testResults.ComprehensiveContentInSeparate / $maxComprehensive) * 20, 20)
$separateRealDataScore = [math]::Min(($testResults.RealDataInSeparate / $maxRealData) * 15, 15)
$separateFunctionsScore = [math]::Min(($testResults.Functions36InSeparate / $maxFunctions) * 10, 10)
$separateFilesScore = [math]::Min(($testResults.FilesContentInSeparate / $maxFiles) * 5, 5)

$testResults.TotalScore = $mainComprehensiveScore + $mainRealDataScore + $mainFunctionsScore + $mainFilesScore + 
                         $separateComprehensiveScore + $separateRealDataScore + $separateFunctionsScore + $separateFilesScore

# Display final results
Write-Host "`nFINAL ACTUAL REPORT CONTENT TEST RESULTS" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

Write-Host "Report Generation:" -ForegroundColor White
Write-Host "   Main Report Generated: $($testResults.MainReportGenerated)" -ForegroundColor White
Write-Host "   Separate Report Generated: $($testResults.SeparateReportGenerated)" -ForegroundColor White

Write-Host "`nMAIN REPORT Content:" -ForegroundColor White
Write-Host "   Comprehensive Content: $($testResults.ComprehensiveContentInMain)/$maxComprehensive ($([math]::Round($mainComprehensiveScore, 2))/20)" -ForegroundColor White
Write-Host "   Real Data Display: $($testResults.RealDataInMain)/$maxRealData ($([math]::Round($mainRealDataScore, 2))/15)" -ForegroundColor White
Write-Host "   Functions Content: $($testResults.Functions36InMain)/$maxFunctions ($([math]::Round($mainFunctionsScore, 2))/10)" -ForegroundColor White
Write-Host "   Files Content: $($testResults.FilesContentInMain)/$maxFiles ($([math]::Round($mainFilesScore, 2))/5)" -ForegroundColor White

Write-Host "`nSEPARATE REPORT Content:" -ForegroundColor White
Write-Host "   Comprehensive Content: $($testResults.ComprehensiveContentInSeparate)/$maxComprehensive ($([math]::Round($separateComprehensiveScore, 2))/20)" -ForegroundColor White
Write-Host "   Real Data Display: $($testResults.RealDataInSeparate)/$maxRealData ($([math]::Round($separateRealDataScore, 2))/15)" -ForegroundColor White
Write-Host "   Functions Content: $($testResults.Functions36InSeparate)/$maxFunctions ($([math]::Round($separateFunctionsScore, 2))/10)" -ForegroundColor White
Write-Host "   Files Content: $($testResults.FilesContentInSeparate)/$maxFiles ($([math]::Round($separateFilesScore, 2))/5)" -ForegroundColor White

Write-Host "`nTotal Score: $([math]::Round($testResults.TotalScore, 2))/100" -ForegroundColor Yellow

if ($testResults.TotalScore -ge 90) {
    Write-Host "EXCELLENT! Both reports contain comprehensive detailed content!" -ForegroundColor Green
} elseif ($testResults.TotalScore -ge 70) {
    Write-Host "GOOD! Most comprehensive content is present in reports" -ForegroundColor Yellow
} elseif ($testResults.TotalScore -ge 50) {
    Write-Host "PARTIAL! Some comprehensive content is present" -ForegroundColor Yellow
} else {
    Write-Host "NEEDS WORK! Reports lack comprehensive detailed content" -ForegroundColor Red
}

Write-Host "`nActual report content test completed" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
