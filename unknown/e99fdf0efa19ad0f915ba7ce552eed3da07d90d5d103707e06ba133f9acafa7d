<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القالب الشامل الأصلي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; font-size: 12px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار القالب الشامل الأصلي للدوال الـ36</h1>
        
        <div class="section">
            <h2>🧪 اختبار النظام الأصلي</h2>
            <button onclick="testOriginalSystem()">🚀 اختبار النظام الأصلي</button>
            <button onclick="generateFullReport()">📊 إنشاء تقرير كامل</button>
            <button onclick="clearResults()">🧹 مسح النتائج</button>
            <div id="testStatus"></div>
        </div>
        
        <div class="section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="testResults"></div>
        </div>
        
        <div class="section">
            <h2>📋 التقرير الكامل</h2>
            <div id="fullReport"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    
    <script>
        let bugBountyCore = null;
        
        async function initializeBugBounty() {
            try {
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore غير محمل');
                }
                
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة BugBountyCore');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة BugBountyCore:', error);
                return false;
            }
        }
        
        async function testOriginalSystem() {
            const statusDiv = document.getElementById('testStatus');
            const resultsDiv = document.getElementById('testResults');
            
            statusDiv.innerHTML = '<div class="warning">⏳ جاري اختبار النظام الأصلي...</div>';
            resultsDiv.innerHTML = '';
            
            try {
                // تهيئة النظام
                const initialized = await initializeBugBounty();
                if (!initialized) {
                    throw new Error('فشل في تهيئة النظام');
                }
                
                // ثغرة تجريبية
                const testVuln = {
                    name: 'SQL Injection في نموذج تسجيل الدخول',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://example.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1' --",
                    location: 'Login Form'
                };
                
                const testRealData = {
                    payload_used: "admin' OR '1'='1' --",
                    response_received: 'MySQL Error: You have an error in your SQL syntax',
                    impact_observed: 'تم تجاوز المصادقة بنجاح',
                    evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL',
                    injection_point: 'username parameter في POST request',
                    exploitation_result: 'تم الدخول كمدير بدون كلمة مرور'
                };
                
                // تطبيق الدوال الـ36 باستخدام النظام الأصلي
                console.log('🔥 تطبيق الدوال الـ36 باستخدام النظام الأصلي...');
                await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(testVuln, testRealData);
                
                let results = '<h3>📊 نتائج اختبار النظام الأصلي:</h3>';
                
                // فحص النتائج
                results += '<h4>🔍 فحص البيانات المُنتجة:</h4>';
                results += '<div class="result">comprehensive_details: ' + (testVuln.comprehensive_details ? '✅ موجود (' + testVuln.comprehensive_details.length + ' حرف)' : '❌ غير موجود') + '</div>';
                results += '<div class="result">comprehensive_analysis: ' + (testVuln.comprehensive_analysis ? '✅ موجود (' + testVuln.comprehensive_analysis.length + ' حرف)' : '❌ غير موجود') + '</div>';
                results += '<div class="result">security_impact_analysis: ' + (testVuln.security_impact_analysis ? '✅ موجود (' + testVuln.security_impact_analysis.length + ' حرف)' : '❌ غير موجود') + '</div>';
                results += '<div class="result">exploitation_steps: ' + (testVuln.exploitation_steps ? '✅ موجود (' + testVuln.exploitation_steps.length + ' حرف)' : '❌ غير موجود') + '</div>';
                results += '<div class="result">dynamic_impact: ' + (testVuln.dynamic_impact ? '✅ موجود (' + testVuln.dynamic_impact.length + ' حرف)' : '❌ غير موجود') + '</div>';
                
                // عرض عينات من المحتوى
                if (testVuln.comprehensive_details) {
                    results += '<h4>📝 عينة من comprehensive_details:</h4>';
                    results += '<div class="code">' + testVuln.comprehensive_details.substring(0, 500) + '...</div>';
                }
                
                if (testVuln.comprehensive_analysis) {
                    results += '<h4>📝 عينة من comprehensive_analysis:</h4>';
                    results += '<div class="code">' + testVuln.comprehensive_analysis.substring(0, 500) + '...</div>';
                }
                
                statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار النظام الأصلي</div>';
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        async function generateFullReport() {
            const statusDiv = document.getElementById('testStatus');
            const reportDiv = document.getElementById('fullReport');
            
            statusDiv.innerHTML = '<div class="warning">⏳ جاري إنشاء تقرير كامل...</div>';
            reportDiv.innerHTML = '';
            
            try {
                // تهيئة النظام
                const initialized = await initializeBugBounty();
                if (!initialized) {
                    throw new Error('فشل في تهيئة النظام');
                }
                
                // ثغرات تجريبية
                const testVulnerabilities = [
                    {
                        name: 'SQL Injection في نموذج تسجيل الدخول',
                        type: 'SQL Injection',
                        severity: 'Critical',
                        url: 'https://example.com/login.php',
                        parameter: 'username'
                    },
                    {
                        name: 'XSS في حقل البحث',
                        type: 'Cross-Site Scripting',
                        severity: 'High',
                        url: 'https://example.com/search.php',
                        parameter: 'query'
                    }
                ];
                
                // تطبيق الدوال على كل ثغرة
                for (let vuln of testVulnerabilities) {
                    const testRealData = {
                        payload_used: vuln.type === 'SQL Injection' ? "admin' OR '1'='1' --" : '<script>alert("XSS")</script>',
                        response_received: vuln.type === 'SQL Injection' ? 'MySQL Error' : 'Script executed',
                        impact_observed: vuln.type === 'SQL Injection' ? 'Authentication bypass' : 'JavaScript execution',
                        evidence_found: 'Security vulnerability confirmed'
                    };
                    
                    await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, testRealData);
                }
                
                // إنشاء HTML للثغرات باستخدام النظام الأصلي
                console.log('📊 إنشاء HTML للثغرات باستخدام النظام الأصلي...');
                const vulnerabilitiesHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
                
                statusDiv.innerHTML = '<div class="result">✅ تم إنشاء التقرير الكامل</div>';
                reportDiv.innerHTML = '<div class="code">' + vulnerabilitiesHTML.substring(0, 3000) + '...</div>';
                
                // فحص المحتوى
                let analysis = '<h4>🔍 تحليل المحتوى المُنتج:</h4>';
                analysis += '<div class="result">طول HTML المُنتج: ' + vulnerabilitiesHTML.length + ' حرف</div>';
                analysis += '<div class="result">يحتوي على "تم تطبيق الدالة بنجاح": ' + (vulnerabilitiesHTML.includes('تم تطبيق الدالة بنجاح') ? '❌ نعم' : '✅ لا') + '</div>';
                analysis += '<div class="result">يحتوي على "comprehensive_details": ' + (vulnerabilitiesHTML.includes('comprehensive_details') ? '✅ نعم' : '❌ لا') + '</div>';
                analysis += '<div class="result">يحتوي على محتوى شامل: ' + (vulnerabilitiesHTML.length > 5000 ? '✅ نعم' : '❌ لا') + '</div>';
                
                reportDiv.innerHTML += analysis;
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في إنشاء التقرير: ' + error.message + '</div>';
                console.error('خطأ في إنشاء التقرير:', error);
            }
        }
        
        function clearResults() {
            document.getElementById('testStatus').innerHTML = '';
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('fullReport').innerHTML = '';
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة اختبار القالب الشامل الأصلي جاهزة');
        });
    </script>
</body>
</html>
