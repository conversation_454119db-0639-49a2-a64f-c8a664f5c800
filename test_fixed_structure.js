/**
 * اختبار البنية المُصلحة للعرض الشامل التفصيلي
 * للتحقق من أن الكود يعمل بشكل صحيح بعد إصلاح البنية
 */

console.log('🔧 بدء اختبار البنية المُصلحة...');

async function testFixedStructure() {
    try {
        // التحقق من وجود BugBountyCore
        if (typeof BugBountyCore === 'undefined') {
            throw new Error('BugBountyCore غير محمل');
        }
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore بنجاح');
        
        // إنشاء ثغرة تجريبية
        const testVuln = {
            name: 'اختبار البنية المُصلحة',
            type: 'Test',
            severity: 'Medium',
            url: 'https://test.com',
            parameter: 'test'
        };
        
        const testData = {
            payload_used: 'test_payload',
            response_received: 'test_response',
            impact_observed: 'test_impact'
        };
        
        console.log('🔧 تطبيق الدوال الـ36...');
        
        // تطبيق الدوال
        await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(testVuln, testData);
        
        console.log('📊 إنشاء HTML باستخدام البنية المُصلحة...');
        
        // إنشاء HTML
        const html = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
        
        console.log('🔍 تحليل النتائج...');
        
        // تحليل النتائج
        const analysis = {
            htmlGenerated: !!html,
            htmlSize: html ? html.length : 0,
            hasComprehensiveStructure: html ? html.includes('النظام v4.0') : false,
            hasProperFormatting: html ? html.includes('style=') : false,
            noErrors: !html || !html.includes('undefined'),
            hasRealContent: html ? !html.includes('تم تطبيق الدالة بنجاح') : false
        };
        
        console.log('📊 نتائج الاختبار:');
        console.log(`   ✅ تم إنشاء HTML: ${analysis.htmlGenerated ? 'نعم' : 'لا'}`);
        console.log(`   📏 حجم HTML: ${analysis.htmlSize.toLocaleString()} حرف`);
        console.log(`   🏗️ بنية شاملة: ${analysis.hasComprehensiveStructure ? 'نعم' : 'لا'}`);
        console.log(`   🎨 تنسيق صحيح: ${analysis.hasProperFormatting ? 'نعم' : 'لا'}`);
        console.log(`   ❌ بدون أخطاء: ${analysis.noErrors ? 'نعم' : 'لا'}`);
        console.log(`   📝 محتوى حقيقي: ${analysis.hasRealContent ? 'نعم' : 'لا'}`);
        
        // تقييم النجاح
        const successCount = Object.values(analysis).filter(Boolean).length;
        const totalChecks = Object.keys(analysis).length;
        
        console.log(`\n🎯 نقاط النجاح: ${successCount}/${totalChecks}`);
        
        if (successCount >= 5) {
            console.log('🎉 البنية المُصلحة تعمل بشكل ممتاز!');
        } else if (successCount >= 3) {
            console.log('✅ البنية المُصلحة تعمل بشكل جيد');
        } else {
            console.log('⚠️ البنية تحتاج مزيد من الإصلاح');
        }
        
        // عرض عينة من المحتوى
        if (html && html.length > 0) {
            console.log('\n📝 عينة من المحتوى:');
            console.log(html.substring(0, 500) + '...');
        }
        
        return {
            success: true,
            analysis: analysis,
            successScore: successCount,
            totalChecks: totalChecks,
            sampleContent: html ? html.substring(0, 500) : '',
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار البنية:', error);
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// تصدير الدالة للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testFixedStructure };
} else if (typeof window !== 'undefined') {
    window.testFixedStructure = testFixedStructure;
}

console.log('📋 ملف اختبار البنية المُصلحة جاهز');
console.log('🚀 استخدم: testFixedStructure() لبدء الاختبار');
