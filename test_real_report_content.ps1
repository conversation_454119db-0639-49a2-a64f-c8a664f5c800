# Real Report Content Test - Generate actual reports and check comprehensive content
Write-Host "Starting REAL report content test - generating actual reports..." -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Test Results
$testResults = @{
    MainReportGenerated = $false
    SeparateReportGenerated = $false
    ComprehensiveContentFound = 0
    RealDataDisplayFound = 0
    FunctionsContentFound = 0
    FilesContentFound = 0
    TotalScore = 0
}

# Create test HTML file to generate real reports
Write-Host "`nCreating test HTML file to generate real reports..." -ForegroundColor Yellow

$testHTML = @"
<!DOCTYPE html>
<html>
<head>
    <title>Real Report Content Test</title>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
</head>
<body>
    <div id="results"></div>
    <script>
        async function generateRealReports() {
            try {
                console.log('Starting real report generation test...');
                
                // Create BugBountyCore instance
                const bugBountyCore = new BugBountyCore();
                
                // Create test vulnerability
                const testVuln = {
                    name: 'API Authentication Bypass',
                    type: 'Authentication Bypass',
                    severity: 'Medium',
                    url: 'http://testphp.vulnweb.com',
                    parameter: 'auth_token',
                    description: 'Authentication bypass vulnerability discovered',
                    payload: 'bypass_payload_test',
                    evidence: 'Response shows successful bypass'
                };
                
                console.log('Test vulnerability created:', testVuln);
                
                // Apply all 36 comprehensive functions
                console.log('Applying 36 comprehensive functions...');
                
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability ? 
                    bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln) : {};
                
                // Apply comprehensive functions
                if (bugBountyCore.generateComprehensiveDetailsFromRealData) {
                    testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                    console.log('comprehensive_details generated:', typeof testVuln.comprehensive_details, testVuln.comprehensive_details ? testVuln.comprehensive_details.toString().substring(0, 200) : 'null');
                }
                
                if (bugBountyCore.generateDynamicImpactForAnyVulnerability) {
                    testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
                    console.log('dynamic_impact generated:', typeof testVuln.dynamic_impact, testVuln.dynamic_impact ? testVuln.dynamic_impact.toString().substring(0, 200) : 'null');
                }
                
                if (bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive) {
                    testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
                    console.log('exploitation_steps generated:', typeof testVuln.exploitation_steps, testVuln.exploitation_steps ? testVuln.exploitation_steps.toString().substring(0, 200) : 'null');
                }
                
                if (bugBountyCore.generateDynamicRecommendationsForVulnerability) {
                    testVuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln, realData);
                    console.log('dynamic_recommendations generated:', typeof testVuln.dynamic_recommendations, testVuln.dynamic_recommendations ? testVuln.dynamic_recommendations.toString().substring(0, 200) : 'null');
                }
                
                // Apply system files
                if (typeof ImpactVisualizer !== 'undefined') {
                    console.log('Applying ImpactVisualizer...');
                    const impactVisualizer = new ImpactVisualizer(bugBountyCore);
                    testVuln.visual_impact_data = await impactVisualizer.createVulnerabilityVisualization(testVuln, realData);
                    console.log('visual_impact_data generated:', typeof testVuln.visual_impact_data, testVuln.visual_impact_data ? 'Generated' : 'null');
                }
                
                if (typeof TextualImpactAnalyzer !== 'undefined') {
                    console.log('Applying TextualImpactAnalyzer...');
                    const textualAnalyzer = new TextualImpactAnalyzer();
                    testVuln.textual_impact_analysis = await textualAnalyzer.analyzeVulnerabilityImpact(testVuln, realData);
                    console.log('textual_impact_analysis generated:', typeof testVuln.textual_impact_analysis, testVuln.textual_impact_analysis ? 'Generated' : 'null');
                }
                
                // Generate main report
                console.log('Generating main report...');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                console.log('Main report generated - size:', mainReport ? mainReport.length : 0);
                
                // Generate separate report  
                console.log('Generating separate report...');
                const pageData = {
                    page_name: 'Test Page',
                    page_url: 'http://testphp.vulnweb.com',
                    vulnerabilities: [testVuln]
                };
                const separateReport = await bugBountyCore.formatSinglePageReport(pageData);
                console.log('Separate report generated - size:', separateReport ? separateReport.length : 0);
                
                // Save reports for analysis
                window.testReports = {
                    mainReport: mainReport,
                    separateReport: separateReport,
                    testVuln: testVuln,
                    realData: realData
                };
                
                console.log('Reports saved to window.testReports for analysis');
                
                return {
                    success: true,
                    mainReportSize: mainReport ? mainReport.length : 0,
                    separateReportSize: separateReport ? separateReport.length : 0,
                    vulnerability: testVuln
                };
                
            } catch (error) {
                console.error('Error generating reports:', error);
                return { success: false, error: error.message };
            }
        }
        
        // Generate reports immediately
        generateRealReports().then(result => {
            console.log('Report generation completed:', result);
            document.getElementById('results').innerHTML = '<h3>Report generation completed - check console for details</h3>';
        });
    </script>
</body>
</html>
"@

# Save test HTML file
$testFile = "test_real_reports.html"
$testHTML | Out-File -FilePath $testFile -Encoding UTF8
Write-Host "Test HTML file created: $testFile" -ForegroundColor Green

# Start Python server for testing
Write-Host "`nStarting Python server for testing..." -ForegroundColor Yellow
$serverProcess = Start-Process python -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
Start-Sleep -Seconds 3
Write-Host "Python server started on port 3000" -ForegroundColor Green

try {
    # Open test page and wait for execution
    Write-Host "`nOpening test page and waiting for report generation..." -ForegroundColor Yellow
    Start-Process "http://localhost:3000/$testFile"
    Start-Sleep -Seconds 10
    
    Write-Host "Waiting for report generation to complete..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # Check if reports were generated by looking for output files
    Write-Host "`nChecking for generated report files..." -ForegroundColor Yellow
    
    $reportFiles = Get-ChildItem -Path "." -Filter "*Bug_Bounty*" -File | Where-Object { $_.LastWriteTime -gt (Get-Date).AddMinutes(-5) }
    
    if ($reportFiles.Count -gt 0) {
        Write-Host "Found $($reportFiles.Count) recently generated report files:" -ForegroundColor Green
        foreach ($file in $reportFiles) {
            Write-Host "   $($file.Name) - Size: $($file.Length) bytes" -ForegroundColor White
            
            # Check content of the report
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            
            # Check for comprehensive content indicators
            $comprehensiveIndicators = @(
                "comprehensive_description",
                "technical_details",
                "exploitation_results",
                "impact_analysis",
                "security_implications",
                "business_impact",
                "detailed_analysis"
            )
            
            $foundIndicators = 0
            foreach ($indicator in $comprehensiveIndicators) {
                if ($content -match [regex]::Escape($indicator)) {
                    $foundIndicators++
                    Write-Host "      Found comprehensive content: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for real data display modifications
            $realDataIndicators = @(
                "max-height: 200px; overflow-y: auto",
                "JSON.stringify",
                "typeof.*object",
                "comprehensive_details",
                "dynamic_impact"
            )
            
            $foundRealDataIndicators = 0
            foreach ($indicator in $realDataIndicators) {
                if ($content -match $indicator) {
                    $foundRealDataIndicators++
                    Write-Host "      Found real data display: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for 36 functions content
            $functionsIndicators = @(
                "generateComprehensiveDetailsFromRealData",
                "generateDynamicImpactForAnyVulnerability", 
                "generateRealExploitationStepsForVulnerabilityComprehensive",
                "generateDynamicRecommendationsForVulnerability",
                "extractRealDataFromDiscoveredVulnerability"
            )
            
            $foundFunctionsContent = 0
            foreach ($indicator in $functionsIndicators) {
                if ($content -match $indicator) {
                    $foundFunctionsContent++
                    Write-Host "      Found function content: $indicator" -ForegroundColor Green
                }
            }
            
            # Check for system files content
            $filesIndicators = @(
                "impact_visualizer.js",
                "textual_impact_analyzer.js",
                "visual_impact_data",
                "textual_impact_analysis"
            )
            
            $foundFilesContent = 0
            foreach ($indicator in $filesIndicators) {
                if ($content -match $indicator) {
                    $foundFilesContent++
                    Write-Host "      Found system file content: $indicator" -ForegroundColor Green
                }
            }
            
            Write-Host "   Content Analysis:" -ForegroundColor Yellow
            Write-Host "      Comprehensive indicators: $foundIndicators/$($comprehensiveIndicators.Count)" -ForegroundColor White
            Write-Host "      Real data display: $foundRealDataIndicators/$($realDataIndicators.Count)" -ForegroundColor White  
            Write-Host "      Functions content: $foundFunctionsContent/$($functionsIndicators.Count)" -ForegroundColor White
            Write-Host "      Files content: $foundFilesContent/$($filesIndicators.Count)" -ForegroundColor White
            
            $testResults.ComprehensiveContentFound += $foundIndicators
            $testResults.RealDataDisplayFound += $foundRealDataIndicators
            $testResults.FunctionsContentFound += $foundFunctionsContent
            $testResults.FilesContentFound += $foundFilesContent
        }
        
        $testResults.MainReportGenerated = $true
        $testResults.SeparateReportGenerated = $true
        
    } else {
        Write-Host "No recent report files found" -ForegroundColor Red
    }
    
} finally {
    # Stop Python server
    Write-Host "`nStopping Python server..." -ForegroundColor Yellow
    Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Python server stopped" -ForegroundColor Green
    
    # Clean up test file
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "Test file cleaned up" -ForegroundColor Gray
    }
}

# Calculate final score
Write-Host "`nCalculating final score..." -ForegroundColor Yellow

$maxComprehensive = 14  # 7 indicators * 2 reports
$maxRealData = 10       # 5 indicators * 2 reports  
$maxFunctions = 10      # 5 indicators * 2 reports
$maxFiles = 8           # 4 indicators * 2 reports

$comprehensiveScore = [math]::Min(($testResults.ComprehensiveContentFound / $maxComprehensive) * 30, 30)
$realDataScore = [math]::Min(($testResults.RealDataDisplayFound / $maxRealData) * 25, 25)
$functionsScore = [math]::Min(($testResults.FunctionsContentFound / $maxFunctions) * 25, 25)
$filesScore = [math]::Min(($testResults.FilesContentFound / $maxFiles) * 20, 20)

$testResults.TotalScore = $comprehensiveScore + $realDataScore + $functionsScore + $filesScore

# Display final results
Write-Host "`nFINAL REAL CONTENT TEST RESULTS" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

Write-Host "Report Generation:" -ForegroundColor White
Write-Host "   Main Report Generated: $($testResults.MainReportGenerated)" -ForegroundColor White
Write-Host "   Separate Report Generated: $($testResults.SeparateReportGenerated)" -ForegroundColor White

Write-Host "`nContent Analysis:" -ForegroundColor White
Write-Host "   Comprehensive Content: $($testResults.ComprehensiveContentFound)/$maxComprehensive ($([math]::Round($comprehensiveScore, 2))/30 points)" -ForegroundColor White
Write-Host "   Real Data Display: $($testResults.RealDataDisplayFound)/$maxRealData ($([math]::Round($realDataScore, 2))/25 points)" -ForegroundColor White
Write-Host "   Functions Content: $($testResults.FunctionsContentFound)/$maxFunctions ($([math]::Round($functionsScore, 2))/25 points)" -ForegroundColor White
Write-Host "   Files Content: $($testResults.FilesContentFound)/$maxFiles ($([math]::Round($filesScore, 2))/20 points)" -ForegroundColor White

Write-Host "`nTotal Score: $([math]::Round($testResults.TotalScore, 2))/100" -ForegroundColor Yellow

if ($testResults.TotalScore -ge 90) {
    Write-Host "EXCELLENT! Real comprehensive content is being displayed in reports!" -ForegroundColor Green
} elseif ($testResults.TotalScore -ge 70) {
    Write-Host "GOOD! Most comprehensive content is being displayed" -ForegroundColor Yellow
} else {
    Write-Host "NEEDS IMPROVEMENT! Comprehensive content is not being displayed properly" -ForegroundColor Red
}

# Save detailed results
$reportContent = @"
# Real Report Content Test Results
Test Date: $timestamp

## Report Generation:
- Main Report Generated: $($testResults.MainReportGenerated)
- Separate Report Generated: $($testResults.SeparateReportGenerated)

## Content Found in Reports:
- Comprehensive Content: $($testResults.ComprehensiveContentFound)/$maxComprehensive
- Real Data Display: $($testResults.RealDataDisplayFound)/$maxRealData  
- Functions Content: $($testResults.FunctionsContentFound)/$maxFunctions
- Files Content: $($testResults.FilesContentFound)/$maxFiles

## Total Score: $([math]::Round($testResults.TotalScore, 2))/100

## Assessment:
$(if ($testResults.TotalScore -ge 90) { "EXCELLENT! Real comprehensive content is being displayed!" } 
  elseif ($testResults.TotalScore -ge 70) { "GOOD! Most comprehensive content is being displayed" } 
  else { "NEEDS IMPROVEMENT! Comprehensive content is not being displayed properly" })
"@

$reportContent | Out-File -FilePath "real_content_test_report.md" -Encoding UTF8
Write-Host "`nDetailed test report saved to: real_content_test_report.md" -ForegroundColor Green

Write-Host "`nReal content test completed" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
