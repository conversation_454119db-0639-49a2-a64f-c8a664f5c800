/**
 * اختبار النظام الحقيقي للتقارير المُصدرة مع BugBountyCore الحقيقي
 * للتحقق من عرض المحتوى بشكل صحيح ومتناسق وليس متداخل
 * مع استخدام الدوال الـ36 والملفات الشاملة التفصيلية ديناميكياً
 */

const fs = require('fs');
const path = require('path');

console.log('🔥 بدء اختبار النظام الحقيقي للتقارير المُصدرة...');

// تحميل BugBountyCore الحقيقي
let BugBountyCore;
try {
    // قراءة ملف BugBountyCore
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة شاملة للمتصفح
    global.window = {
        addEventListener: () => {},
        removeEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test Environment' },
        document: null
    };

    global.document = {
        createElement: (tag) => ({
            style: {},
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null,
            innerHTML: '',
            textContent: '',
            tagName: tag.toUpperCase()
        }),
        body: {
            appendChild: () => {},
            style: {}
        },
        getElementById: () => null,
        querySelector: () => null,
        querySelectorAll: () => [],
        addEventListener: () => {},
        removeEventListener: () => {}
    };

    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };

    global.sessionStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };

    global.console = console;
    global.alert = () => {};
    global.confirm = () => true;
    global.prompt = () => '';

    // ربط document بـ window
    global.window.document = global.document;
    
    // تنفيذ الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore || window.BugBountyCore;
    
    if (!BugBountyCore) {
        throw new Error('لم يتم العثور على BugBountyCore في الملف');
    }
    
    console.log('✅ تم تحميل BugBountyCore الحقيقي بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// بيانات ثغرات شاملة للاختبار
const comprehensiveTestVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        location: 'Login Form',
        method: 'POST'
    },
    {
        name: 'Cross-Site Scripting في حقل البحث',
        type: 'XSS',
        severity: 'High',
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>',
        location: 'Search Form',
        method: 'GET'
    },
    {
        name: 'CSRF في تغيير كلمة المرور',
        type: 'CSRF',
        severity: 'Medium',
        url: 'https://example.com/change-password.php',
        parameter: 'new_password',
        payload: '<form action="..." method="post">',
        location: 'Password Change Form',
        method: 'POST'
    }
];

// بيانات حقيقية شاملة لكل نوع ثغرة
const comprehensiveRealDataByType = {
    'SQL Injection': {
        payload_used: "admin' OR '1'='1' --",
        response_received: 'MySQL Error: You have an error in your SQL syntax near OR at line 1',
        impact_observed: 'تم تجاوز المصادقة بنجاح والوصول لحساب المدير',
        evidence_found: 'رسائل خطأ قاعدة البيانات كشفت عن بنية SQL والجداول',
        injection_point: 'username parameter في POST request',
        exploitation_result: 'تم الدخول كمدير بدون كلمة مرور صحيحة',
        database_info: 'MySQL 5.7.33, database: webapp_db, table: users',
        extracted_data: 'usernames, password hashes, email addresses',
        technical_details: 'Boolean-based blind SQL injection vulnerability',
        business_impact: 'Complete compromise of user authentication system',
        security_implications: 'Unauthorized access to all user accounts and sensitive data'
    },
    'XSS': {
        payload_used: '<script>alert("XSS Vulnerability Found")</script>',
        response_received: 'تم عرض الكود JavaScript في الصفحة بدون تنظيف',
        impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
        evidence_found: 'ظهور نافذة alert تؤكد تنفيذ الكود',
        injection_point: 'query parameter في GET request',
        exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
        browser_info: 'Chrome 91.0, Firefox 89.0 - vulnerable in both',
        session_impact: 'يمكن سرقة session tokens والتحكم في الحساب',
        technical_details: 'Reflected XSS vulnerability in search functionality',
        business_impact: 'User session hijacking and data theft potential',
        security_implications: 'Client-side code execution and user impersonation'
    },
    'CSRF': {
        payload_used: '<form action="https://example.com/change-password.php" method="post"><input name="new_password" value="hacked123"></form>',
        response_received: 'تم تغيير كلمة المرور بنجاح بدون تحقق من CSRF token',
        impact_observed: 'تغيير كلمة مرور المستخدم من موقع خارجي',
        evidence_found: 'عدم وجود CSRF protection في النموذج',
        injection_point: 'change-password form عبر external website',
        exploitation_result: 'تم تغيير كلمة المرور إلى "hacked123"',
        attack_vector: 'malicious website hosting the CSRF form',
        protection_missing: 'no CSRF tokens, no referrer validation',
        technical_details: 'Cross-Site Request Forgery in password change functionality',
        business_impact: 'Unauthorized account modifications and potential account takeover',
        security_implications: 'User actions can be performed without consent'
    }
};

// دالة تحليل التقرير المُصدر الحقيقي
function analyzeRealExportedReport(reportHTML, reportType) {
    const analysis = {
        type: reportType,
        totalSize: reportHTML.length,
        hasComprehensiveStructure: false,
        hasAllFunctionGroups: false,
        hasComprehensiveFiles: false,
        hasSystemSummary: false,
        isWellFormatted: false,
        isNotOverlapping: false,
        hasRealDynamicContent: false,
        hasProperCSS: false,
        contentQuality: 'unknown',
        structureScore: 0,
        issues: []
    };
    
    // فحص البنية الشاملة
    const comprehensiveIndicators = [
        'النظام v4.0 - جميع الدوال الـ36 والملفات الشاملة',
        'المجموعة الأولى: الدوال الأساسية الشاملة',
        'المجموعة الثانية: دوال التحليل المتقدم',
        'المجموعة الثالثة: دوال التحليل التفاعلي والبصري',
        'المجموعة الرابعة: دوال النظام المثابر والأدلة',
        'المجموعة الخامسة: دوال التحليل المتقدم والخبراء'
    ];
    
    let structureCount = 0;
    comprehensiveIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            structureCount++;
        }
    });
    
    analysis.hasComprehensiveStructure = structureCount >= 4;
    analysis.hasAllFunctionGroups = structureCount >= 5;
    
    // فحص الملفات الشاملة
    const comprehensiveFiles = [
        'الملفات الشاملة التفصيلية من النظام v4',
        'impact_visualizer.js',
        'textual_impact_analyzer.js'
    ];
    
    let filesCount = 0;
    comprehensiveFiles.forEach(file => {
        if (reportHTML.includes(file)) {
            filesCount++;
        }
    });
    
    analysis.hasComprehensiveFiles = filesCount >= 2;
    
    // فحص ملخص النظام
    analysis.hasSystemSummary = reportHTML.includes('ملخص شامل لحالة النظام v4.0');
    
    // فحص التنسيق
    const formatIndicators = [
        'style="background: linear-gradient',
        'border-radius:',
        'padding:',
        'margin:'
    ];
    
    let formatCount = 0;
    formatIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            formatCount++;
        }
    });
    
    analysis.isWellFormatted = formatCount >= 3;
    analysis.hasProperCSS = formatCount >= 4;
    
    // فحص عدم التداخل
    const divOpenCount = (reportHTML.match(/<div[^>]*>/g) || []).length;
    const divCloseCount = (reportHTML.match(/<\/div>/g) || []).length;
    analysis.isNotOverlapping = Math.abs(divOpenCount - divCloseCount) <= 2;
    
    if (!analysis.isNotOverlapping) {
        analysis.issues.push(`عدم توازن في عناصر div: ${divOpenCount} فتح، ${divCloseCount} إغلاق`);
    }
    
    // فحص المحتوى الديناميكي الحقيقي
    const realContentIndicators = [
        'MySQL Error',
        'alert("XSS")',
        'CSRF token',
        'admin\' OR \'1\'=\'1\'',
        'exploitation_result',
        'technical_details'
    ];
    
    let realContentCount = 0;
    realContentIndicators.forEach(indicator => {
        if (reportHTML.includes(indicator)) {
            realContentCount++;
        }
    });
    
    analysis.hasRealDynamicContent = realContentCount >= 3;
    
    // فحص الرسائل العامة (يجب ألا تكون موجودة)
    const genericMessages = [
        'تم تطبيق الدالة بنجاح',
        'تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح'
    ];
    
    genericMessages.forEach(msg => {
        if (reportHTML.includes(msg)) {
            analysis.issues.push(`يحتوي على رسالة عامة: ${msg}`);
        }
    });
    
    // حساب نقاط البنية
    analysis.structureScore = [
        analysis.hasComprehensiveStructure,
        analysis.hasAllFunctionGroups,
        analysis.hasComprehensiveFiles,
        analysis.hasSystemSummary,
        analysis.isWellFormatted,
        analysis.isNotOverlapping,
        analysis.hasRealDynamicContent,
        analysis.hasProperCSS
    ].filter(Boolean).length;
    
    // تقييم جودة المحتوى
    if (analysis.structureScore >= 7) {
        analysis.contentQuality = 'excellent';
    } else if (analysis.structureScore >= 5) {
        analysis.contentQuality = 'good';
    } else if (analysis.structureScore >= 3) {
        analysis.contentQuality = 'fair';
    } else {
        analysis.contentQuality = 'poor';
    }
    
    return analysis;
}

// دالة الاختبار الرئيسية
async function testRealSystemReports() {
    console.log('🧪 بدء اختبار النظام الحقيقي للتقارير المُصدرة...');
    
    try {
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore الحقيقي');
        
        // تطبيق الدوال الـ36 على جميع الثغرات
        console.log('🔥 تطبيق الدوال الـ36 والملفات الشاملة على جميع الثغرات...');
        
        for (let i = 0; i < comprehensiveTestVulnerabilities.length; i++) {
            const vuln = comprehensiveTestVulnerabilities[i];
            const realData = comprehensiveRealDataByType[vuln.type];
            
            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            // تطبيق جميع الدوال الـ36 والملفات الشاملة
            if (bugBountyCore.applyAllComprehensiveFunctionsToVulnerability) {
                await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
                console.log(`✅ تم تطبيق الدوال على: ${vuln.name}`);
            } else {
                console.log(`⚠️ دالة applyAllComprehensiveFunctionsToVulnerability غير متاحة`);
            }
        }
        
        // اختبار التقرير الرئيسي
        console.log('📋 اختبار التقرير الرئيسي المُصدر...');
        const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(comprehensiveTestVulnerabilities);
        
        // تحليل التقرير الرئيسي
        const mainReportAnalysis = analyzeRealExportedReport(mainReportHTML, 'التقرير الرئيسي');
        console.log('📊 تحليل التقرير الرئيسي:', mainReportAnalysis);
        
        // اختبار التقرير المنفصل
        console.log('📄 اختبار التقرير المنفصل المُصدر...');
        const separateReportHTML = await bugBountyCore.formatSinglePageReport({
            page_name: 'صفحة الاختبار الشامل',
            page_url: 'https://example.com/comprehensive-test',
            vulnerabilities: comprehensiveTestVulnerabilities
        });
        
        // تحليل التقرير المنفصل
        const separateReportAnalysis = analyzeRealExportedReport(separateReportHTML, 'التقرير المنفصل');
        console.log('📊 تحليل التقرير المنفصل:', separateReportAnalysis);
        
        // النتائج
        console.log('\n📊 نتائج اختبار النظام الحقيقي:');
        console.log('='.repeat(60));
        
        console.log(`\n📋 التقرير الرئيسي:`);
        console.log(`   📏 الحجم: ${mainReportAnalysis.totalSize.toLocaleString()} حرف`);
        console.log(`   🎯 جودة المحتوى: ${mainReportAnalysis.contentQuality}`);
        console.log(`   📊 نقاط البنية: ${mainReportAnalysis.structureScore}/8`);
        console.log(`   🏗️ بنية شاملة: ${mainReportAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال: ${mainReportAnalysis.hasAllFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${mainReportAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${mainReportAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   ❌ غير متداخل: ${mainReportAnalysis.isNotOverlapping ? '✅' : '❌'}`);
        console.log(`   📝 محتوى ديناميكي: ${mainReportAnalysis.hasRealDynamicContent ? '✅' : '❌'}`);
        
        console.log(`\n📄 التقرير المنفصل:`);
        console.log(`   📏 الحجم: ${separateReportAnalysis.totalSize.toLocaleString()} حرف`);
        console.log(`   🎯 جودة المحتوى: ${separateReportAnalysis.contentQuality}`);
        console.log(`   📊 نقاط البنية: ${separateReportAnalysis.structureScore}/8`);
        console.log(`   🏗️ بنية شاملة: ${separateReportAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال: ${separateReportAnalysis.hasAllFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${separateReportAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${separateReportAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   ❌ غير متداخل: ${separateReportAnalysis.isNotOverlapping ? '✅' : '❌'}`);
        console.log(`   📝 محتوى ديناميكي: ${separateReportAnalysis.hasRealDynamicContent ? '✅' : '❌'}`);
        
        // مقارنة التقارير
        console.log(`\n🔍 مقارنة التقارير:`);
        console.log(`   📏 فرق الحجم: ${Math.abs(mainReportAnalysis.totalSize - separateReportAnalysis.totalSize).toLocaleString()} حرف`);
        console.log(`   🎯 جودة متطابقة: ${mainReportAnalysis.contentQuality === separateReportAnalysis.contentQuality ? '✅' : '❌'}`);
        console.log(`   🏗️ بنية متناسقة: ${mainReportAnalysis.hasComprehensiveStructure && separateReportAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📝 محتوى حقيقي في كليهما: ${mainReportAnalysis.hasRealDynamicContent && separateReportAnalysis.hasRealDynamicContent ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق جيد في كليهما: ${mainReportAnalysis.isWellFormatted && separateReportAnalysis.isWellFormatted ? '✅' : '❌'}`);
        
        // عرض المشاكل إن وجدت
        if (mainReportAnalysis.issues.length > 0) {
            console.log(`\n⚠️ مشاكل التقرير الرئيسي:`);
            mainReportAnalysis.issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue}`);
            });
        }
        
        if (separateReportAnalysis.issues.length > 0) {
            console.log(`\n⚠️ مشاكل التقرير المنفصل:`);
            separateReportAnalysis.issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue}`);
            });
        }
        
        // النتيجة النهائية
        const overallSuccess = mainReportAnalysis.contentQuality !== 'poor' && separateReportAnalysis.contentQuality !== 'poor';
        const bothExcellent = mainReportAnalysis.contentQuality === 'excellent' && separateReportAnalysis.contentQuality === 'excellent';
        
        console.log(`\n🎉 النتيجة النهائية: ${overallSuccess ? 'نجح الاختبار ✅' : 'فشل الاختبار ❌'}`);
        
        if (bothExcellent) {
            console.log(`🌟 كلا التقريرين ممتاز! التقرير المنفصل شامل مثل التقرير الرئيسي تماماً!`);
        } else if (mainReportAnalysis.contentQuality === 'excellent' && separateReportAnalysis.contentQuality !== 'excellent') {
            console.log(`⚠️ التقرير الرئيسي ممتاز لكن التقرير المنفصل يحتاج تحسين!`);
        }
        
        // حفظ التقارير للفحص
        fs.writeFileSync('main_report_real_test.html', mainReportHTML, 'utf8');
        fs.writeFileSync('separate_report_real_test.html', separateReportHTML, 'utf8');
        
        console.log(`\n💾 تم حفظ التقارير:`);
        console.log(`   📋 التقرير الرئيسي: main_report_real_test.html`);
        console.log(`   📄 التقرير المنفصل: separate_report_real_test.html`);
        
        // عرض عينات من المحتوى
        console.log(`\n📝 عينة من التقرير الرئيسي (أول 300 حرف):`);
        console.log(mainReportHTML.substring(0, 300) + '...');
        
        console.log(`\n📝 عينة من التقرير المنفصل (أول 300 حرف):`);
        console.log(separateReportHTML.substring(0, 300) + '...');
        
        return {
            success: overallSuccess,
            mainReport: mainReportAnalysis,
            separateReport: separateReportAnalysis,
            bothExcellent: bothExcellent,
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام الحقيقي:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testRealSystemReports().then(result => {
    console.log('\n✅ انتهى اختبار النظام الحقيقي');
    console.log(`📊 النتيجة: ${result.success ? 'نجح' : 'فشل'}`);
    if (result.bothExcellent) {
        console.log(`🎉 كلا التقريرين ممتاز! المهمة مكتملة!`);
    }
    process.exit(result.success ? 0 : 1);
}).catch(error => {
    console.error('❌ خطأ عام:', error);
    process.exit(1);
});
