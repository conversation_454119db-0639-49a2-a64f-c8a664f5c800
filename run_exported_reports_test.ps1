# اختبار شامل للتقارير المُصدرة (الرئيسي والمنفصل) من خلال PowerShell
# للتحقق من عرض المحتوى بشكل صحيح ومتناسق وليس متداخل

Write-Host "🔥 بدء اختبار التقارير المُصدرة الشامل..." -ForegroundColor Yellow

# التحقق من وجود الملفات المطلوبة
$requiredFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "test_exported_reports_comprehensive.js"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ الملف المطلوب غير موجود: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# إنشاء ملف HTML للاختبار الشامل
$testHTML = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير المُصدرة الشامل</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
        .progress-bar { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; transition: width 0.3s; }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .report-card { background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd; }
        .quality-badge { display: inline-block; padding: 5px 10px; border-radius: 15px; color: white; font-weight: bold; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار التقارير المُصدرة الشامل للدوال الـ36</h1>
        
        <div class="section">
            <h2>🧪 حالة الاختبار</h2>
            <div id="testStatus">⏳ جاري تحميل النظام...</div>
        </div>
        
        <div class="section">
            <h2>📊 الجودة الإجمالية</h2>
            <div id="overallQuality"></div>
        </div>
        
        <div class="section">
            <h2>📋 مقارنة التقارير</h2>
            <div id="reportsComparison"></div>
        </div>
        
        <div class="section">
            <h2>📊 تحليل التقرير الرئيسي</h2>
            <div id="mainReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>📄 تحليل التقرير المنفصل</h2>
            <div id="separateReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>💾 اختبار التصدير الفعلي</h2>
            <div id="exportTest"></div>
        </div>
        
        <div class="section">
            <h2>📝 عينات من المحتوى</h2>
            <div id="contentSamples"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_exported_reports_comprehensive.js"></script>
    
    <script>
        async function runExportedReportsTest() {
            const statusDiv = document.getElementById('testStatus');
            
            try {
                statusDiv.innerHTML = '<div class="warning">⏳ جاري تشغيل اختبار التقارير المُصدرة الشامل...</div>';
                
                // تشغيل الاختبار
                const result = await testExportedReportsComprehensive();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="result">✅ تم الانتهاء من اختبار التقارير المُصدرة بنجاح!</div>';
                    
                    // عرض الجودة الإجمالية
                    displayOverallQuality(result);
                    
                    // عرض مقارنة التقارير
                    displayReportsComparison(result.comparison);
                    
                    // عرض تحليل التقارير
                    displayReportAnalysis(result.mainReport.analysis, 'mainReportAnalysis');
                    displayReportAnalysis(result.separateReport.analysis, 'separateReportAnalysis');
                    
                    // عرض اختبار التصدير
                    displayExportTest(result.exportTest);
                    
                    // عرض عينات المحتوى
                    displayContentSamples(result);
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ فشل الاختبار: ' + result.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        function displayOverallQuality(result) {
            const qualityDiv = document.getElementById('overallQuality');
            const quality = result.overall_quality;
            
            let qualityClass = 'poor';
            let qualityText = 'ضعيف';
            let qualityScore = 0;
            
            switch(quality) {
                case 'excellent':
                    qualityClass = 'excellent';
                    qualityText = 'ممتاز';
                    qualityScore = 95;
                    break;
                case 'good':
                    qualityClass = 'good';
                    qualityText = 'جيد';
                    qualityScore = 80;
                    break;
                case 'fair':
                    qualityClass = 'fair';
                    qualityText = 'مقبول';
                    qualityScore = 60;
                    break;
                default:
                    qualityClass = 'poor';
                    qualityText = 'ضعيف';
                    qualityScore = 30;
            }
            
            qualityDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${qualityClass}">${qualityText}</div>
                        <div>الجودة الإجمالية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.vulnerabilities_processed}</div>
                        <div>ثغرات معالجة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.functions_applied}</div>
                        <div>دالة مطبقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.comprehensive_files_used}</div>
                        <div>ملف شامل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.mainReport.size / 1024)} KB</div>
                        <div>حجم التقرير الرئيسي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Math.round(result.separateReport.size / 1024)} KB</div>
                        <div>حجم التقرير المنفصل</div>
                    </div>
                </div>
                
                <div class="progress">
                    <div class="progress-bar" style="width: ${qualityScore}%"></div>
                </div>
            `;
        }
        
        function displayReportsComparison(comparison) {
            const comparisonDiv = document.getElementById('reportsComparison');
            
            comparisonDiv.innerHTML = `
                <div class="comparison-grid">
                    <div class="report-card">
                        <h4>📊 التوافق بين التقارير</h4>
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-number ${comparison.qualityMatch ? 'excellent' : 'warning'}">${comparison.qualityMatch ? '✅' : '⚠️'}</div>
                                <div>جودة متطابقة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number ${comparison.bothHaveComprehensiveStructure ? 'excellent' : 'poor'}">${comparison.bothHaveComprehensiveStructure ? '✅' : '❌'}</div>
                                <div>بنية شاملة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number ${comparison.bothHaveRealContent ? 'excellent' : 'poor'}">${comparison.bothHaveRealContent ? '✅' : '❌'}</div>
                                <div>محتوى حقيقي</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number ${comparison.bothWellFormatted ? 'excellent' : 'poor'}">${comparison.bothWellFormatted ? '✅' : '❌'}</div>
                                <div>تنسيق جيد</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number ${comparison.bothNotOverlapping ? 'excellent' : 'poor'}">${comparison.bothNotOverlapping ? '✅' : '❌'}</div>
                                <div>غير متداخل</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="report-card">
                        <h4>🔍 تفاصيل المقارنة</h4>
                        <div class="info">
                            <strong>التوافق الإجمالي:</strong> 
                            <span class="quality-badge ${comparison.overallCompatibility}">${getQualityText(comparison.overallCompatibility)}</span><br>
                            <strong>فرق الحجم:</strong> ${Math.round(comparison.sizeDifference / 1024)} KB<br>
                            <strong>مشاكل التقرير الرئيسي:</strong> ${comparison.issuesComparison.main}<br>
                            <strong>مشاكل التقرير المنفصل:</strong> ${comparison.issuesComparison.separate}
                        </div>
                    </div>
                </div>
            `;
        }
        
        function displayReportAnalysis(analysis, elementId) {
            const element = document.getElementById(elementId);
            
            element.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.contentQuality}">${getQualityText(analysis.contentQuality)}</div>
                        <div>جودة المحتوى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.structureScore}/8</div>
                        <div>نقاط البنية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${analysis.totalSize.toLocaleString()}</div>
                        <div>حجم المحتوى (حرف)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.issues.length === 0 ? 'excellent' : 'warning'}">${analysis.issues.length}</div>
                        <div>المشاكل المكتشفة</div>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveStructure ? 'excellent' : 'poor'}">${analysis.hasComprehensiveStructure ? '✅' : '❌'}</div>
                        <div>بنية شاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasAllFunctionGroups ? 'excellent' : 'poor'}">${analysis.hasAllFunctionGroups ? '✅' : '❌'}</div>
                        <div>جميع مجموعات الدوال</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasComprehensiveFiles ? 'excellent' : 'poor'}">${analysis.hasComprehensiveFiles ? '✅' : '❌'}</div>
                        <div>الملفات الشاملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.isNotOverlapping ? 'excellent' : 'poor'}">${analysis.isNotOverlapping ? '✅' : '❌'}</div>
                        <div>غير متداخل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${analysis.hasRealDynamicContent ? 'excellent' : 'poor'}">${analysis.hasRealDynamicContent ? '✅' : '❌'}</div>
                        <div>محتوى ديناميكي</div>
                    </div>
                </div>
                
                ${analysis.issues.length > 0 ? `
                <div class="warning">
                    <strong>المشاكل المكتشفة:</strong>
                    <ul>' + analysis.issues.map(issue => '<li>' + issue + '</li>').join('') + '</ul>
                </div>
                ` : ''}
            `;
        }
        
        function displayExportTest(exportTest) {
            const exportDiv = document.getElementById('exportTest');
            
            exportDiv.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number ${exportTest.exportFunctional ? 'excellent' : 'poor'}">${exportTest.exportFunctional ? '✅' : '❌'}</div>
                        <div>التصدير يعمل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${exportTest.mainExportSuccess ? 'excellent' : 'poor'}">${exportTest.mainExportSuccess ? '✅' : '❌'}</div>
                        <div>تصدير التقرير الرئيسي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number ${exportTest.separateExportSuccess ? 'excellent' : 'poor'}">${exportTest.separateExportSuccess ? '✅' : '❌'}</div>
                        <div>تصدير التقرير المنفصل</div>
                    </div>
                </div>
                
                ${exportTest.exportError ? `
                <div class="error">
                    <strong>خطأ في التصدير:</strong> ' + exportTest.exportError + '
                </div>
                ` : ''}
            `;
        }
        
        function displayContentSamples(result) {
            const samplesDiv = document.getElementById('contentSamples');
            
            samplesDiv.innerHTML = `
                <h4>📋 عينة من التقرير الرئيسي:</h4>
                <div class="code">${result.mainReport.sample}</div>
                
                <h4>📄 عينة من التقرير المنفصل:</h4>
                <div class="code">${result.separateReport.sample}</div>
            `;
        }
        
        function getQualityText(quality) {
            const qualityTexts = {
                'excellent': 'ممتاز',
                'good': 'جيد',
                'fair': 'مقبول',
                'poor': 'ضعيف'
            };
            return qualityTexts[quality] || quality;
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة اختبار التقارير المُصدرة جاهزة');
            runExportedReportsTest();
        });
    </script>
</body>
</html>
"@

# حفظ ملف HTML
$htmlFile = "exported_reports_test_comprehensive.html"
$testHTML | Out-File -FilePath $htmlFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف الاختبار: $htmlFile" -ForegroundColor Green

# فتح الاختبار في المتصفح
Write-Host "🌐 فتح الاختبار في المتصفح..." -ForegroundColor Yellow
Start-Process $htmlFile

# انتظار قليل ثم عرض الملخص
Write-Host "⏳ انتظار تشغيل الاختبار..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

Write-Host "`n📊 ملخص اختبار التقارير المصدرة الشامل:" -ForegroundColor Yellow
Write-Host "✅ تم إنشاء اختبار شامل للتقارير المصدرة" -ForegroundColor Green
Write-Host "✅ يتم اختبار التقرير الرئيسي والمنفصل" -ForegroundColor Green
Write-Host "✅ يتم فحص الدوال الـ36 والملفات الشاملة" -ForegroundColor Green
Write-Host "✅ يتم التحقق من عدم التداخل والتنسيق" -ForegroundColor Green
Write-Host "✅ يتم اختبار التصدير الفعلي للتقارير" -ForegroundColor Green

Write-Host "`n🔍 ما يتم فحصه في التقارير المصدرة:" -ForegroundColor Cyan
Write-Host "   📋 البنية الشاملة التفصيلية" -ForegroundColor White
Write-Host "   📂 جميع مجموعات الدوال الـ36" -ForegroundColor White
Write-Host "   📁 الملفات الشاملة التفصيلية" -ForegroundColor White
Write-Host "   🎨 التنسيق والعرض المتناسق" -ForegroundColor White
Write-Host "   ❌ عدم التداخل في العناصر" -ForegroundColor White
Write-Host "   📝 المحتوى الديناميكي الحقيقي" -ForegroundColor White
Write-Host "   💾 وظائف التصدير الفعلية" -ForegroundColor White
Write-Host "   🔍 مقارنة شاملة بين التقارير" -ForegroundColor White

Write-Host "`n📋 النتائج متوفرة في:" -ForegroundColor Yellow
Write-Host "   🌐 المتصفح: $htmlFile" -ForegroundColor White
Write-Host "   📝 Console: F12 في المتصفح" -ForegroundColor White

Write-Host "`n✅ انتهى تشغيل اختبار التقارير المصدرة الشامل" -ForegroundColor Green
