# Run JavaScript Test for Report Content
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Running JavaScript Test for Report Content..." -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Check if Node.js is available
Write-Host "`nChecking Node.js availability..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "   ✅ Node.js found: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Node.js not found - trying alternative method" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Node.js not available - using browser-based test" -ForegroundColor Red
    $nodeVersion = $null
}

if ($nodeVersion) {
    # Run Node.js test
    Write-Host "`nRunning Node.js test..." -ForegroundColor Yellow
    
    try {
        $testOutput = node test_report_content.js 2>&1
        Write-Host "Test Output:" -ForegroundColor Cyan
        $testOutput | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
        
        # Check if test results file was created
        if (Test-Path "test_results.json") {
            Write-Host "`nReading test results..." -ForegroundColor Yellow
            $results = Get-Content "test_results.json" -Raw -Encoding UTF8 | ConvertFrom-Json
            
            Write-Host "`nDetailed Results:" -ForegroundColor Cyan
            Write-Host "   Timestamp: $($results.timestamp)" -ForegroundColor White
            Write-Host "   Main Report Size: $($results.mainReportSize) characters" -ForegroundColor White
            Write-Host "   Separate Report Size: $($results.separateReportSize) characters" -ForegroundColor White
            Write-Host "   Total Score: $($results.testResults.totalScore)/100" -ForegroundColor White
            
            if ($results.testResults.totalScore -ge 90) {
                Write-Host "`n🎉 EXCELLENT! Reports contain comprehensive detailed content!" -ForegroundColor Green
            } elseif ($results.testResults.totalScore -ge 70) {
                Write-Host "`n👍 GOOD! Most comprehensive content is present!" -ForegroundColor Yellow
            } else {
                Write-Host "`n❌ NEEDS WORK! Reports lack comprehensive content!" -ForegroundColor Red
            }
        }
        
    } catch {
        Write-Host "   ❌ Error running Node.js test: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    # Create browser-based test
    Write-Host "`nCreating browser-based test..." -ForegroundColor Yellow
    
    $browserTestHTML = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Report Content Test</title>
    <script src="assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
</head>
<body>
    <div id="results"></div>
    <script>
        async function testReportContent() {
            try {
                console.log('🔥 بدء اختبار المحتوى الحقيقي للتقارير المُصدرة...');
                
                const bugBountyCore = new BugBountyCore();
                
                // Create test vulnerability
                const testVuln = {
                    name: 'API Authentication Bypass',
                    type: 'Authentication Bypass',
                    severity: 'Medium',
                    url: 'http://testphp.vulnweb.com',
                    parameter: 'auth_token',
                    description: 'ثغرة تجاوز المصادقة في API',
                    payload: 'admin\\'||1=1--',
                    evidence: 'استجابة الخادم تشير إلى نجاح تجاوز المصادقة'
                };
                
                console.log('🎯 تطبيق الدوال الـ36 الشاملة التفصيلية...');
                
                // Extract real data
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability ? 
                    bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln) : {};
                
                console.log('📋 البيانات الحقيقية المستخرجة:', realData);
                
                // Apply comprehensive functions
                if (bugBountyCore.generateComprehensiveDetailsFromRealData) {
                    testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                    console.log('✅ comprehensive_details:', testVuln.comprehensive_details);
                }
                
                if (bugBountyCore.generateDynamicImpactForAnyVulnerability) {
                    testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
                    console.log('✅ dynamic_impact:', testVuln.dynamic_impact);
                }
                
                if (bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive) {
                    testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
                    console.log('✅ exploitation_steps:', testVuln.exploitation_steps);
                }
                
                // Apply system files
                if (typeof ImpactVisualizer !== 'undefined') {
                    const impactVisualizer = new ImpactVisualizer(bugBountyCore);
                    testVuln.visual_impact_data = await impactVisualizer.createVulnerabilityVisualization(testVuln, realData);
                    console.log('✅ visual_impact_data:', testVuln.visual_impact_data);
                }
                
                if (typeof TextualImpactAnalyzer !== 'undefined') {
                    const textualAnalyzer = new TextualImpactAnalyzer();
                    testVuln.textual_impact_analysis = await textualAnalyzer.analyzeVulnerabilityImpact(testVuln, realData);
                    console.log('✅ textual_impact_analysis:', testVuln.textual_impact_analysis);
                }
                
                // Generate reports
                console.log('📄 إنشاء التقارير...');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                
                const pageData = {
                    page_name: 'صفحة الاختبار',
                    page_url: 'http://testphp.vulnweb.com',
                    vulnerabilities: [testVuln]
                };
                const separateReport = await bugBountyCore.formatSinglePageReport(pageData);
                
                // Analyze content
                const testResults = {
                    mainReportGenerated: !!mainReport,
                    separateReportGenerated: !!separateReport,
                    mainReportSize: mainReport ? mainReport.length : 0,
                    separateReportSize: separateReport ? separateReport.length : 0,
                    comprehensiveContentFound: 0,
                    realDataDisplayFound: 0,
                    functions36ContentFound: 0,
                    filesContentFound: 0
                };
                
                // Check main report content
                if (mainReport) {
                    const comprehensiveIndicators = [
                        'تحليل شامل تفصيلي', 'تفاصيل الاكتشاف الحقيقية', 'comprehensive_description', 'technical_details'
                    ];
                    const realDataIndicators = [
                        'max-height: 200px', 'JSON.stringify', 'comprehensive_details', 'dynamic_impact'
                    ];
                    const functionsIndicators = [
                        'generateComprehensiveDetailsFromRealData', 'generateDynamicImpactForAnyVulnerability'
                    ];
                    const filesIndicators = [
                        'impact_visualizer.js', 'textual_impact_analyzer.js', 'visual_impact_data'
                    ];
                    
                    comprehensiveIndicators.forEach(indicator => {
                        if (mainReport.includes(indicator)) testResults.comprehensiveContentFound++;
                    });
                    
                    realDataIndicators.forEach(indicator => {
                        if (mainReport.includes(indicator)) testResults.realDataDisplayFound++;
                    });
                    
                    functionsIndicators.forEach(indicator => {
                        if (mainReport.includes(indicator)) testResults.functions36ContentFound++;
                    });
                    
                    filesIndicators.forEach(indicator => {
                        if (mainReport.includes(indicator)) testResults.filesContentFound++;
                    });
                }
                
                // Calculate score
                const totalScore = (testResults.comprehensiveContentFound * 25) + 
                                 (testResults.realDataDisplayFound * 25) + 
                                 (testResults.functions36ContentFound * 25) + 
                                 (testResults.filesContentFound * 25);
                
                // Display results
                const resultsHTML = \`
                    <h2>🏆 نتائج اختبار محتوى التقارير</h2>
                    <div style="font-family: Arial, sans-serif; direction: rtl;">
                        <h3>📊 حالة التقارير:</h3>
                        <p>التقرير الرئيسي: \${testResults.mainReportGenerated ? '✅ تم إنشاؤه' : '❌ لم يتم إنشاؤه'}</p>
                        <p>التقرير المنفصل: \${testResults.separateReportGenerated ? '✅ تم إنشاؤه' : '❌ لم يتم إنشاؤه'}</p>
                        <p>حجم التقرير الرئيسي: \${testResults.mainReportSize} حرف</p>
                        <p>حجم التقرير المنفصل: \${testResults.separateReportSize} حرف</p>
                        
                        <h3>📋 تحليل المحتوى:</h3>
                        <p>المحتوى الشامل: \${testResults.comprehensiveContentFound}/4</p>
                        <p>عرض البيانات الحقيقية: \${testResults.realDataDisplayFound}/4</p>
                        <p>محتوى الدوال الـ36: \${testResults.functions36ContentFound}/2</p>
                        <p>محتوى الملفات: \${testResults.filesContentFound}/3</p>
                        
                        <h3>🎯 النتيجة الإجمالية: \${totalScore}/100</h3>
                        
                        <div style="padding: 10px; margin: 10px 0; border-radius: 5px; \${totalScore >= 90 ? 'background: #d4edda; color: #155724;' : totalScore >= 70 ? 'background: #fff3cd; color: #856404;' : 'background: #f8d7da; color: #721c24;'}">
                            \${totalScore >= 90 ? '🎉 ممتاز! التقارير تحتوي على محتوى شامل تفصيلي حقيقي!' : 
                              totalScore >= 70 ? '👍 جيد! معظم المحتوى الشامل موجود!' : 
                              '❌ يحتاج عمل! التقارير تفتقر للمحتوى الشامل!'}
                        </div>
                    </div>
                \`;
                
                document.getElementById('results').innerHTML = resultsHTML;
                
                console.log('✅ اكتمل اختبار محتوى التقارير');
                console.log('النتيجة الإجمالية:', totalScore, '/100');
                
                return testResults;
                
            } catch (error) {
                console.error('❌ خطأ في اختبار المحتوى:', error);
                document.getElementById('results').innerHTML = '<h3 style="color: red;">خطأ: ' + error.message + '</h3>';
                return { success: false, error: error.message };
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', () => {
            testReportContent();
        });
    </script>
</body>
</html>
"@
    
    # Save browser test
    $browserTestHTML | Out-File -FilePath "browser_test.html" -Encoding UTF8
    Write-Host "   ✅ Browser test created: browser_test.html" -ForegroundColor Green
    
    # Start Python server
    Write-Host "`nStarting Python server..." -ForegroundColor Yellow
    $serverProcess = Start-Process python -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    
    try {
        # Open browser test
        Write-Host "`nOpening browser test..." -ForegroundColor Yellow
        Start-Process "http://localhost:3000/browser_test.html"
        
        Write-Host "Waiting for test completion (30 seconds)..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
        
        Write-Host "`n✅ Browser test completed. Check the browser window for results." -ForegroundColor Green
        
    } finally {
        # Stop server and cleanup
        Write-Host "`nStopping server and cleaning up..." -ForegroundColor Yellow
        Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
        if (Test-Path "browser_test.html") {
            Remove-Item "browser_test.html" -Force
        }
    }
}

Write-Host "`n🎯 Test Summary:" -ForegroundColor Cyan
Write-Host "This test checks if the exported reports contain:" -ForegroundColor White
Write-Host "   1. Real comprehensive detailed content from 36 functions" -ForegroundColor White
Write-Host "   2. Actual data display modifications (not just function names)" -ForegroundColor White
Write-Host "   3. System files content (impact_visualizer.js, textual_impact_analyzer.js)" -ForegroundColor White
Write-Host "   4. Arabic comprehensive content" -ForegroundColor White

Write-Host "`nIf the score is below 70, it means:" -ForegroundColor Yellow
Write-Host "   - Functions are producing simple messages instead of detailed content" -ForegroundColor Yellow
Write-Host "   - Reports show 'تم تطبيق الدالة بنجاح' instead of real analysis" -ForegroundColor Yellow
Write-Host "   - System files are not properly integrated" -ForegroundColor Yellow

$currentTime = Get-Date -Format 'HH:mm:ss'
Write-Host "`nJavaScript test completed at $currentTime" -ForegroundColor Green
