/**
 * اختبار نهائي للتأكد من أن كلاً من التقرير الرئيسي والمنفصل
 * يعملان بنفس الجودة مع النظام v4.0 الشامل التفصيلي
 */

const fs = require('fs');

console.log('🎯 اختبار نهائي للتقارير الرئيسي والمنفصل...');

// تحميل BugBountyCore الحقيقي
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة شاملة للمتصفح
    global.window = {
        addEventListener: () => {},
        removeEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test Environment' },
        document: null
    };
    
    global.document = {
        createElement: (tag) => ({
            style: {},
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null,
            innerHTML: '',
            textContent: '',
            tagName: tag.toUpperCase()
        }),
        body: { 
            appendChild: () => {},
            style: {}
        },
        head: {
            appendChild: () => {},
            style: {}
        },
        getElementById: () => null,
        querySelector: () => null,
        querySelectorAll: () => [],
        addEventListener: () => {},
        removeEventListener: () => {}
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.sessionStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.console = console;
    global.alert = () => {};
    global.confirm = () => true;
    global.prompt = () => '';
    global.window.document = global.document;
    
    // تنفيذ الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore || window.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore الحقيقي بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// بيانات ثغرات للاختبار النهائي
const testVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --"
    },
    {
        name: 'Cross-Site Scripting في حقل البحث',
        type: 'XSS',
        severity: 'High',
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>'
    }
];

const realDataByType = {
    'SQL Injection': {
        payload_used: "admin' OR '1'='1' --",
        response_received: 'MySQL Error: You have an error in your SQL syntax',
        impact_observed: 'تم تجاوز المصادقة بنجاح',
        exploitation_result: 'تم الدخول كمدير بدون كلمة مرور',
        technical_details: 'Boolean-based blind SQL injection vulnerability',
        business_impact: 'Complete compromise of user authentication system',
        security_implications: 'Unauthorized access to all user accounts'
    },
    'XSS': {
        payload_used: '<script>alert("XSS")</script>',
        response_received: 'تم عرض الكود JavaScript في الصفحة',
        impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
        exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
        technical_details: 'Reflected XSS vulnerability in search functionality',
        business_impact: 'User session hijacking and data theft potential',
        security_implications: 'Client-side code execution and user impersonation'
    }
};

async function runFinalTest() {
    try {
        console.log('🧪 بدء الاختبار النهائي...');
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // تطبيق الدوال الـ36 على جميع الثغرات
        console.log('🔥 تطبيق الدوال الـ36 على جميع الثغرات...');
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            const realData = realDataByType[vuln.type];
            
            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            if (bugBountyCore.applyAllComprehensiveFunctionsToVulnerability) {
                await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
                console.log(`✅ تم تطبيق الدوال على: ${vuln.name}`);
            }
        }
        
        // اختبار التقرير الرئيسي
        console.log('\n📋 اختبار التقرير الرئيسي...');
        const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
        
        // اختبار التقرير المنفصل
        console.log('📄 اختبار التقرير المنفصل...');
        const separateReportHTML = await bugBountyCore.formatSinglePageReport({
            page_name: 'صفحة الاختبار النهائي',
            page_url: 'https://example.com/final-test',
            vulnerabilities: testVulnerabilities
        });
        
        // تحليل النتائج
        console.log('\n📊 تحليل النتائج النهائية:');
        console.log('='.repeat(60));
        
        // تحليل التقرير الرئيسي
        const mainAnalysis = {
            size: mainReportHTML.length,
            hasComprehensiveStructure: mainReportHTML.includes('النظام v4.0'),
            hasFunctionGroups: mainReportHTML.includes('المجموعة الأولى'),
            hasComprehensiveFiles: mainReportHTML.includes('الملفات الشاملة التفصيلية'),
            hasSystemSummary: mainReportHTML.includes('ملخص شامل'),
            hasRealData: mainReportHTML.includes("admin' OR '1'='1'") && mainReportHTML.includes('<script>alert("XSS")</script>'),
            isWellFormatted: mainReportHTML.includes('style='),
            hasNoGenericMessages: !mainReportHTML.includes('تم تطبيق الدالة بنجاح')
        };
        
        // تحليل التقرير المنفصل
        const separateAnalysis = {
            size: separateReportHTML.length,
            hasComprehensiveStructure: separateReportHTML.includes('النظام v4.0'),
            hasFunctionGroups: separateReportHTML.includes('المجموعة الأولى'),
            hasComprehensiveFiles: separateReportHTML.includes('الملفات الشاملة التفصيلية'),
            hasSystemSummary: separateReportHTML.includes('ملخص شامل'),
            hasRealData: separateReportHTML.includes("admin' OR '1'='1'") && separateReportHTML.includes('<script>alert("XSS")</script>'),
            isWellFormatted: separateReportHTML.includes('style='),
            hasNoGenericMessages: !separateReportHTML.includes('تم تطبيق الدالة بنجاح')
        };
        
        // عرض النتائج
        console.log(`📋 التقرير الرئيسي:`);
        console.log(`   📏 الحجم: ${mainAnalysis.size.toLocaleString()} حرف (${Math.round(mainAnalysis.size/1024)} KB)`);
        console.log(`   🏗️ بنية شاملة v4.0: ${mainAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال الـ36: ${mainAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${mainAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${mainAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   📝 بيانات حقيقية ديناميكية: ${mainAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق جيد: ${mainAnalysis.isWellFormatted ? '✅' : '❌'}`);
        console.log(`   🚫 بدون نصوص عامة: ${mainAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        
        console.log(`\n📄 التقرير المنفصل:`);
        console.log(`   📏 الحجم: ${separateAnalysis.size.toLocaleString()} حرف (${Math.round(separateAnalysis.size/1024)} KB)`);
        console.log(`   🏗️ بنية شاملة v4.0: ${separateAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال الـ36: ${separateAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${separateAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${separateAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   📝 بيانات حقيقية ديناميكية: ${separateAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق جيد: ${separateAnalysis.isWellFormatted ? '✅' : '❌'}`);
        console.log(`   🚫 بدون نصوص عامة: ${separateAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        
        // مقارنة التقارير
        console.log(`\n🔍 مقارنة التقارير:`);
        console.log(`   📏 فرق الحجم: ${Math.abs(mainAnalysis.size - separateAnalysis.size).toLocaleString()} حرف`);
        console.log(`   🎯 كلاهما شامل: ${mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 كلاهما يحتوي الدوال الـ36: ${mainAnalysis.hasFunctionGroups && separateAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📝 كلاهما يحتوي بيانات حقيقية: ${mainAnalysis.hasRealData && separateAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🚫 كلاهما بدون نصوص عامة: ${mainAnalysis.hasNoGenericMessages && separateAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        
        // حساب النقاط
        const mainScore = Object.values(mainAnalysis).filter(Boolean).length;
        const separateScore = Object.values(separateAnalysis).filter(Boolean).length;
        
        console.log(`\n🎯 النقاط:`);
        console.log(`   📋 التقرير الرئيسي: ${mainScore}/8`);
        console.log(`   📄 التقرير المنفصل: ${separateScore}/8`);
        
        // النتيجة النهائية
        const bothExcellent = mainScore >= 7 && separateScore >= 7;
        const bothGood = mainScore >= 6 && separateScore >= 6;
        
        if (bothExcellent) {
            console.log(`\n🎉 النتيجة النهائية: ممتاز! كلا التقريرين يعملان بكفاءة عالية!`);
            console.log(`✅ النظام v4.0 الشامل التفصيلي يعمل بنجاح`);
            console.log(`✅ الدوال الـ36 تطبق ديناميكياً حسب الثغرة المكتشفة`);
            console.log(`✅ البيانات الحقيقية تُمرر وتُعرض بنجاح`);
            console.log(`✅ القالب الشامل الأصلي يعمل بكفاءة`);
            console.log(`✅ بدون نصوص عامة أو افتراضية`);
        } else if (bothGood) {
            console.log(`\n✅ النتيجة النهائية: جيد! كلا التقريرين يعملان بشكل مقبول`);
        } else {
            console.log(`\n⚠️ النتيجة النهائية: يحتاج تحسين`);
        }
        
        // حفظ التقارير للفحص
        fs.writeFileSync('final_main_report.html', mainReportHTML, 'utf8');
        fs.writeFileSync('final_separate_report.html', separateReportHTML, 'utf8');
        
        console.log(`\n💾 تم حفظ التقارير النهائية:`);
        console.log(`   📋 التقرير الرئيسي: final_main_report.html`);
        console.log(`   📄 التقرير المنفصل: final_separate_report.html`);
        
        return {
            success: bothGood,
            excellent: bothExcellent,
            mainReport: mainAnalysis,
            separateReport: separateAnalysis,
            mainScore: mainScore,
            separateScore: separateScore
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار النهائي
runFinalTest().then(result => {
    console.log('\n🏁 انتهى الاختبار النهائي');
    if (result.excellent) {
        console.log('🌟 النظام يعمل بكفاءة ممتازة!');
        process.exit(0);
    } else if (result.success) {
        console.log('✅ النظام يعمل بشكل جيد');
        process.exit(0);
    } else {
        console.log('❌ النظام يحتاج مزيد من التحسين');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ خطأ عام:', error);
    process.exit(1);
});
