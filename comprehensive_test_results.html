﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ط§ط®طھط¨ط§ط± ط§ظ„طھظ‚ط§ط±ظٹط± ط§ظ„ط´ط§ظ…ظ„</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .result { background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
        .code { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ًں”¥ ط§ط®طھط¨ط§ط± ط§ظ„طھظ‚ط§ط±ظٹط± ط§ظ„ط´ط§ظ…ظ„ ظ„ظ„ط¯ظˆط§ظ„ ط§ظ„ظ€36</h1>
        
        <div class="section">
            <h2>ًں§ھ ط­ط§ظ„ط© ط§ظ„ط§ط®طھط¨ط§ط±</h2>
            <div id="testStatus">âڈ³ ط¬ط§ط±ظٹ طھط­ظ…ظٹظ„ ط§ظ„ظ†ط¸ط§ظ…...</div>
        </div>
        
        <div class="section">
            <h2>ًں“ٹ ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„طھظ‚ط§ط±ظٹط±</h2>
            <div id="reportStats"></div>
        </div>
        
        <div class="section">
            <h2>ًں“‹ طھط­ظ„ظٹظ„ ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ط±ط¦ظٹط³ظٹ</h2>
            <div id="mainReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>ًں“„ طھط­ظ„ظٹظ„ ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ظ…ظ†ظپطµظ„</h2>
            <div id="separateReportAnalysis"></div>
        </div>
        
        <div class="section">
            <h2>ًں”چ ظ…ظ‚ط§ط±ظ†ط© ط§ظ„طھظ‚ط§ط±ظٹط±</h2>
            <div id="reportsComparison"></div>
        </div>
        
        <div class="section">
            <h2>ًں’، ط§ظ„طھظˆطµظٹط§طھ</h2>
            <div id="recommendations"></div>
        </div>
        
        <div class="section">
            <h2>ًں“‌ ط¹ظٹظ†ط§طھ ظ…ظ† ط§ظ„ظ…ط­طھظˆظ‰</h2>
            <div id="contentSamples"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_reports_comprehensive.js"></script>
    
    <script>
        async function runComprehensiveTest() {
            const statusDiv = document.getElementById('testStatus');
            
            try {
                statusDiv.innerHTML = '<div class="warning">âڈ³ ط¬ط§ط±ظٹ طھط´ط؛ظٹظ„ ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„ط´ط§ظ…ظ„...</div>';
                
                // طھط´ط؛ظٹظ„ ط§ظ„ط§ط®طھط¨ط§ط±
                const result = await testComprehensiveReports();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="result">âœ… طھظ… ط§ظ„ط§ظ†طھظ‡ط§ط، ظ…ظ† ط§ظ„ط§ط®طھط¨ط§ط± ط¨ظ†ط¬ط§ط­!</div>';
                    
                    // ط¹ط±ط¶ ط§ظ„ط¥ط­طµط§ط¦ظٹط§طھ
                    displayReportStats(result);
                    
                    // ط¹ط±ط¶ طھط­ظ„ظٹظ„ ط§ظ„طھظ‚ط§ط±ظٹط±
                    displayReportAnalysis(result.mainReport.analysis, 'mainReportAnalysis');
                    displayReportAnalysis(result.separateReport.analysis, 'separateReportAnalysis');
                    
                    // ط¹ط±ط¶ ط§ظ„ظ…ظ‚ط§ط±ظ†ط©
                    displayComparison(result.comparison);
                    
                    // ط¹ط±ط¶ ط§ظ„طھظˆطµظٹط§طھ
                    displayRecommendations(result.comparison.recommendation);
                    
                    // ط¹ط±ط¶ ط¹ظٹظ†ط§طھ ط§ظ„ظ…ط­طھظˆظ‰
                    displayContentSamples(result);
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">â‌Œ ظپط´ظ„ ط§ظ„ط§ط®طھط¨ط§ط±: ' + result.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">â‌Œ ط®ط·ط£ ظپظٹ طھط´ط؛ظٹظ„ ط§ظ„ط§ط®طھط¨ط§ط±: ' + error.message + '</div>';
                console.error('ط®ط·ط£ ظپظٹ ط§ظ„ط§ط®طھط¨ط§ط±:', error);
            }
        }
        
        function displayReportStats(result) {
            const statsDiv = document.getElementById('reportStats');
            statsDiv.innerHTML = 
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number"></div>
                        <div>ط«ط؛ط±ط§طھ ظ…ط¹ط§ظ„ط¬ط©</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"></div>
                        <div>ط¯ط§ظ„ط© ظ…ط·ط¨ظ‚ط©</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"> KB</div>
                        <div>ط­ط¬ظ… ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ط±ط¦ظٹط³ظٹ</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"> KB</div>
                        <div>ط­ط¬ظ… ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ظ…ظ†ظپطµظ„</div>
                    </div>
                </div>
            ;
        }
        
        function displayReportAnalysis(analysis, elementId) {
            const element = document.getElementById(elementId);
            const qualityClass = analysis.contentQuality;
            
            let html = 
                <div class="info">
                    <strong>ظ†ظˆط¹ ط§ظ„طھظ‚ط±ظٹط±:</strong> <br>
                    <strong>ط­ط¬ظ… ط§ظ„ظ…ط­طھظˆظ‰:</strong>  ط­ط±ظپ<br>
                    <strong>ط¬ظˆط¯ط© ط§ظ„ظ…ط­طھظˆظ‰:</strong> <span class=""></span>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ظ…ط­طھظˆظ‰ ط´ط§ظ…ظ„</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ط¨ظٹط§ظ†ط§طھ ط­ظ‚ظٹظ‚ظٹط©</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>طھظ†ط³ظٹظ‚ ط¬ظٹط¯</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ط¨ط¯ظˆظ† ط±ط³ط§ط¦ظ„ ط¹ط§ظ…ط©</div>
                    </div>
                </div>
            ;
            
            if (analysis.issues.length > 0) {
                html += '<div class="warning"><strong>ط§ظ„ظ…ط´ط§ظƒظ„ ط§ظ„ظ…ظƒطھط´ظپط©:</strong><ul>';
                analysis.issues.forEach(issue => {
                    html += <li></li>;
                });
                html += '</ul></div>';
            }
            
            element.innerHTML = html;
        }
        
        function displayComparison(comparison) {
            const element = document.getElementById('reportsComparison');
            
            element.innerHTML = 
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ط¬ظˆط¯ط© ظ…طھط·ط§ط¨ظ‚ط©</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ظ…ط­طھظˆظ‰ ط´ط§ظ…ظ„ ظپظٹ ظƒظ„ظٹظ‡ظ…ط§</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number "></div>
                        <div>ط¨ظٹط§ظ†ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظپظٹ ظƒظ„ظٹظ‡ظ…ط§</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"> KB</div>
                        <div>ظپط±ظ‚ ط§ظ„ط­ط¬ظ…</div>
                    </div>
                </div>
                
                <div class="info">
                    <strong>ظ…ط´ط§ظƒظ„ ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ط±ط¦ظٹط³ظٹ:</strong> <br>
                    <strong>ظ…ط´ط§ظƒظ„ ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ظ…ظ†ظپطµظ„:</strong> 
                </div>
            ;
        }
        
        function displayRecommendations(recommendations) {
            const element = document.getElementById('recommendations');
            
            let html = '<ul>';
            recommendations.forEach(rec => {
                html += <li></li>;
            });
            html += '</ul>';
            
            element.innerHTML = html;
        }
        
        function displayContentSamples(result) {
            const element = document.getElementById('contentSamples');
            
            element.innerHTML = 
                <h4>ًں“‹ ط¹ظٹظ†ط© ظ…ظ† ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ط±ط¦ظٹط³ظٹ:</h4>
                <div class="code"></div>
                
                <h4>ًں“„ ط¹ظٹظ†ط© ظ…ظ† ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ظ…ظ†ظپطµظ„:</h4>
                <div class="code"></div>
            ;
        }
        
        function getQualityText(quality) {
            const qualityTexts = {
                'excellent': 'ظ…ظ…طھط§ط²',
                'good': 'ط¬ظٹط¯',
                'fair': 'ظ…ظ‚ط¨ظˆظ„',
                'poor': 'ط¶ط¹ظٹظپ'
            };
            return qualityTexts[quality] || quality;
        }
        
        // طھط´ط؛ظٹظ„ ط§ظ„ط§ط®طھط¨ط§ط± ط¹ظ†ط¯ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©
        window.addEventListener('load', function() {
            console.log('ًںŒگ طµظپط­ط© ط§ظ„ط§ط®طھط¨ط§ط± ط¬ط§ظ‡ط²ط©');
            runComprehensiveTest();
        });
    </script>
</body>
</html>
