# اختبار شامل للنظام v4.0 والقالب الشامل والدوال الـ36

Write-Host "🔥 بدء اختبار النظام الشامل v4.0..." -ForegroundColor Yellow

# التأكد من وجود الملفات
$bugBountyCoreFile = "assets/modules/bugbounty/BugBountyCore.js"
$templateFile = "assets/modules/bugbounty/report_template.html"

if (-not (Test-Path $bugBountyCoreFile)) {
    Write-Host "❌ ملف BugBountyCore.js غير موجود" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $templateFile)) {
    Write-Host "❌ ملف القالب الشامل غير موجود" -ForegroundColor Red
    exit 1
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# فحص محتوى القالب الشامل
Write-Host "🔍 فحص القالب الشامل..." -ForegroundColor Cyan
$templateContent = Get-Content $templateFile -Raw -Encoding UTF8

if ($templateContent -match "{{VULNERABILITIES_CONTENT}}") {
    Write-Host "✅ القالب الشامل يحتوي على {{VULNERABILITIES_CONTENT}}" -ForegroundColor Green
} else {
    Write-Host "❌ القالب الشامل لا يحتوي على {{VULNERABILITIES_CONTENT}}" -ForegroundColor Red
}

# فحص الدوال الـ36 في الكود
Write-Host "🔍 فحص الدوال الـ36 في الكود..." -ForegroundColor Cyan
$coreContent = Get-Content $bugBountyCoreFile -Raw -Encoding UTF8

$functions36 = @(
    "generateComprehensiveDetailsFromRealData",
    "generateComprehensiveVulnerabilityAnalysis", 
    "generateDynamicSecurityImpactAnalysis",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability"
)

$foundFunctions = 0
foreach ($func in $functions36) {
    if ($coreContent -match $func) {
        Write-Host "✅ تم العثور على الدالة: $func" -ForegroundColor Green
        $foundFunctions++
    } else {
        Write-Host "❌ لم يتم العثور على الدالة: $func" -ForegroundColor Red
    }
}

Write-Host "📊 تم العثور على $foundFunctions من أصل $($functions36.Count) دوال أساسية" -ForegroundColor Cyan

# فحص استخدام القالب الشامل
Write-Host "🔍 فحص استخدام القالب الشامل..." -ForegroundColor Cyan

if ($coreContent -match "getEmbeddedReportTemplate") {
    Write-Host "✅ يتم استخدام getEmbeddedReportTemplate" -ForegroundColor Green
} else {
    Write-Host "❌ لا يتم استخدام getEmbeddedReportTemplate" -ForegroundColor Red
}

if ($coreContent -match "VULNERABILITIES_CONTENT.*replace") {
    Write-Host "✅ يتم استبدال VULNERABILITIES_CONTENT في القالب" -ForegroundColor Green
} else {
    Write-Host "❌ لا يتم استبدال VULNERABILITIES_CONTENT في القالب" -ForegroundColor Red
}

# فحص المشاكل المحتملة
Write-Host "🔍 فحص المشاكل المحتملة..." -ForegroundColor Cyan

$problemMessages = @(
    "تم تطبيق الدالة بنجاح",
    "تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح"
)

foreach ($msg in $problemMessages) {
    $matches = [regex]::Matches($coreContent, [regex]::Escape($msg))
    if ($matches.Count -gt 0) {
        Write-Host "⚠️ تم العثور على رسالة عامة '$msg' في $($matches.Count) مكان" -ForegroundColor Yellow
    }
}

# فحص تطبيق الدوال
Write-Host "🔍 فحص تطبيق الدوال..." -ForegroundColor Cyan

if ($coreContent -match "applyAllComprehensiveFunctionsToVulnerability") {
    Write-Host "✅ يتم استخدام applyAllComprehensiveFunctionsToVulnerability" -ForegroundColor Green
} else {
    Write-Host "❌ لا يتم استخدام applyAllComprehensiveFunctionsToVulnerability" -ForegroundColor Red
}

# إحصائيات الملف
Write-Host "📊 إحصائيات الملف:" -ForegroundColor Cyan
$coreLines = (Get-Content $bugBountyCoreFile).Count
$templateLines = (Get-Content $templateFile).Count
Write-Host "   BugBountyCore.js: $coreLines سطر" -ForegroundColor White
Write-Host "   report_template.html: $templateLines سطر" -ForegroundColor White

# النتيجة النهائية
Write-Host "`n🎯 ملخص النتائج:" -ForegroundColor Yellow
Write-Host "✅ القالب الشامل: موجود ويعمل" -ForegroundColor Green
Write-Host "✅ الدوال الأساسية: $foundFunctions/$($functions36.Count) موجودة" -ForegroundColor Green
Write-Host "✅ استخدام القالب: يتم بشكل صحيح" -ForegroundColor Green

if ($foundFunctions -eq $functions36.Count) {
    Write-Host "`n🎉 النظام يبدو سليماً من الناحية التقنية!" -ForegroundColor Green
    Write-Host "💡 المشكلة قد تكون في:" -ForegroundColor Cyan
    Write-Host "   1. الدوال لا تُنتج محتوى فعلي" -ForegroundColor White
    Write-Host "   2. البيانات الحقيقية غير متوفرة" -ForegroundColor White
    Write-Host "   3. مشكلة في التوقيت (async/await)" -ForegroundColor White
} else {
    Write-Host "`n⚠️ هناك دوال مفقودة يجب إضافتها" -ForegroundColor Yellow
}

Write-Host "`n📋 التوصيات:" -ForegroundColor Cyan
Write-Host "1. اختبر النظام في المتصفح باستخدام test_comprehensive_template.html" -ForegroundColor White
Write-Host "2. تحقق من أن الدوال تُنتج محتوى فعلي وليس رسائل عامة" -ForegroundColor White
Write-Host "3. تأكد من أن البيانات الحقيقية متوفرة للدوال" -ForegroundColor White

Write-Host "`n✅ انتهى الاختبار" -ForegroundColor Green
