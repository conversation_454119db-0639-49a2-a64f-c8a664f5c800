/**
 * اختبار شامل للدوال الـ36 والملفات في نظام Bug Bounty v4.0
 * للتحقق من سبب عدم عرض المحتوى الشامل التفصيلي
 */

console.log('🔥 بدء اختبار الدوال الـ36 الشاملة التفصيلية...');

// محاكاة ثغرة للاختبار
const testVulnerability = {
    name: 'SQL Injection Test',
    type: 'SQL Injection',
    severity: 'Critical',
    url: 'https://example.com/login',
    parameter: 'username',
    payload: "' OR 1=1 --",
    response: 'Database error revealed',
    location: 'Login form'
};

const testRealData = {
    payload_used: "' OR 1=1 --",
    response_received: 'Database error: syntax error near OR',
    impact_observed: 'Authentication bypass successful',
    evidence_found: 'Error messages revealed database structure',
    injection_point: 'username parameter',
    exploitation_result: 'Successfully bypassed authentication'
};

async function testComprehensiveFunctions() {
    console.log('🧪 اختبار الدوال الشاملة التفصيلية...');
    
    try {
        // تحميل BugBountyCore
        if (typeof window !== 'undefined' && window.BugBountyCore) {
            const bugBountyCore = new window.BugBountyCore();
            
            console.log('✅ تم تحميل BugBountyCore بنجاح');
            
            // اختبار الدالة الأولى: generateComprehensiveDetailsFromRealData
            console.log('🔍 اختبار generateComprehensiveDetailsFromRealData...');
            const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVulnerability, testRealData);
            
            console.log('📊 نتيجة generateComprehensiveDetailsFromRealData:');
            console.log('النوع:', typeof comprehensiveDetails);
            console.log('الطول:', comprehensiveDetails ? comprehensiveDetails.length : 'undefined');
            console.log('المحتوى (أول 200 حرف):', comprehensiveDetails ? comprehensiveDetails.substring(0, 200) : 'لا يوجد محتوى');
            
            // اختبار الدالة الثانية: generateComprehensiveVulnerabilityAnalysis
            console.log('🔍 اختبار generateComprehensiveVulnerabilityAnalysis...');
            const comprehensiveAnalysis = await bugBountyCore.generateComprehensiveVulnerabilityAnalysis(testVulnerability, testRealData);
            
            console.log('📊 نتيجة generateComprehensiveVulnerabilityAnalysis:');
            console.log('النوع:', typeof comprehensiveAnalysis);
            console.log('الطول:', comprehensiveAnalysis ? comprehensiveAnalysis.length : 'undefined');
            console.log('المحتوى (أول 200 حرف):', comprehensiveAnalysis ? comprehensiveAnalysis.substring(0, 200) : 'لا يوجد محتوى');
            
            // اختبار الدالة الثالثة: generateDynamicSecurityImpactAnalysis
            console.log('🔍 اختبار generateDynamicSecurityImpactAnalysis...');
            const securityImpactAnalysis = await bugBountyCore.generateDynamicSecurityImpactAnalysis(testVulnerability, testRealData);
            
            console.log('📊 نتيجة generateDynamicSecurityImpactAnalysis:');
            console.log('النوع:', typeof securityImpactAnalysis);
            console.log('الطول:', securityImpactAnalysis ? securityImpactAnalysis.length : 'undefined');
            console.log('المحتوى (أول 200 حرف):', securityImpactAnalysis ? securityImpactAnalysis.substring(0, 200) : 'لا يوجد محتوى');
            
            // اختبار تطبيق الدوال على كائن الثغرة
            console.log('🔧 اختبار تطبيق الدوال على كائن الثغرة...');
            const testVuln = { ...testVulnerability };
            
            // تطبيق الدوال
            testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
            testVuln.comprehensive_analysis = await bugBountyCore.generateComprehensiveVulnerabilityAnalysis(testVuln, testRealData);
            testVuln.security_impact_analysis = await bugBountyCore.generateDynamicSecurityImpactAnalysis(testVuln, testRealData);
            
            console.log('📋 نتائج تطبيق الدوال على كائن الثغرة:');
            console.log('comprehensive_details موجود:', !!testVuln.comprehensive_details);
            console.log('comprehensive_details نوع:', typeof testVuln.comprehensive_details);
            console.log('comprehensive_details طول:', testVuln.comprehensive_details ? testVuln.comprehensive_details.length : 'undefined');
            
            console.log('comprehensive_analysis موجود:', !!testVuln.comprehensive_analysis);
            console.log('comprehensive_analysis نوع:', typeof testVuln.comprehensive_analysis);
            console.log('comprehensive_analysis طول:', testVuln.comprehensive_analysis ? testVuln.comprehensive_analysis.length : 'undefined');
            
            console.log('security_impact_analysis موجود:', !!testVuln.security_impact_analysis);
            console.log('security_impact_analysis نوع:', typeof testVuln.security_impact_analysis);
            console.log('security_impact_analysis طول:', testVuln.security_impact_analysis ? testVuln.security_impact_analysis.length : 'undefined');
            
            // اختبار العرض في HTML
            console.log('🎨 اختبار العرض في HTML...');
            const htmlDisplay1 = testVuln.comprehensive_details || 'تم تطبيق الدالة بنجاح';
            const htmlDisplay2 = testVuln.comprehensive_analysis || 'تم التحليل الشامل للثغرة';
            const htmlDisplay3 = testVuln.security_impact_analysis || 'تم تحليل التأثير الأمني';
            
            console.log('HTML Display 1 (comprehensive_details):', htmlDisplay1.substring(0, 100));
            console.log('HTML Display 2 (comprehensive_analysis):', htmlDisplay2.substring(0, 100));
            console.log('HTML Display 3 (security_impact_analysis):', htmlDisplay3.substring(0, 100));
            
            // تحليل المشكلة
            console.log('🔍 تحليل المشكلة:');
            if (testVuln.comprehensive_details && testVuln.comprehensive_details.length > 50) {
                console.log('✅ الدوال تعمل بشكل صحيح وتُنتج محتوى شامل');
                console.log('❌ المشكلة في العرض أو في حفظ النتائج');
            } else {
                console.log('❌ الدوال لا تُنتج محتوى شامل');
                console.log('🔧 يجب إصلاح الدوال نفسها');
            }
            
            return {
                success: true,
                comprehensiveDetails: testVuln.comprehensive_details,
                comprehensiveAnalysis: testVuln.comprehensive_analysis,
                securityImpactAnalysis: testVuln.security_impact_analysis
            };
            
        } else {
            console.error('❌ BugBountyCore غير متاح');
            return { success: false, error: 'BugBountyCore غير متاح' };
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
if (typeof window !== 'undefined') {
    // في المتصفح
    window.testComprehensiveFunctions = testComprehensiveFunctions;
    console.log('🌐 الاختبار جاهز للتشغيل في المتصفح');
    console.log('استخدم: testComprehensiveFunctions()');
} else {
    // في Node.js
    testComprehensiveFunctions().then(result => {
        console.log('🎯 نتيجة الاختبار:', result);
    }).catch(error => {
        console.error('❌ خطأ في تشغيل الاختبار:', error);
    });
}
