
        <div class="single-page-report" style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
            <h3>📄 التقرير المنفصل: صفحة الاختبار</h3>
            <p><strong>الصفحة:</strong> https://example.com/test</p>
            <p><strong>عدد الثغرات:</strong> 2</p>
            
            
            <div class="vuln-summary" style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #17a2b8;">
                <h5>1. SQL Injection في نموذج تسجيل الدخول</h5>
                <p><strong>النوع:</strong> SQL Injection</p>
                <p><strong>الخطورة:</strong> Critical</p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0;">
                    <strong>التفاصيل الشاملة:</strong><br>
                    تفاصيل شاملة للثغرة SQL Injection في نموذج تسجيل الدخول باستخدام البيانات: admin' OR '1'='1' --
                </div>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0;">
                    <strong>التحليل الشامل:</strong><br>
                    تحليل شامل للثغرة SQL Injection مع التأثير: تم تجاوز المصادقة بنجاح
                </div>
            </div>
            
            <div class="vuln-summary" style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #17a2b8;">
                <h5>2. Cross-Site Scripting في حقل البحث</h5>
                <p><strong>النوع:</strong> XSS</p>
                <p><strong>الخطورة:</strong> High</p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0;">
                    <strong>التفاصيل الشاملة:</strong><br>
                    تفاصيل شاملة للثغرة Cross-Site Scripting في حقل البحث باستخدام البيانات: <script>alert("XSS")</script>
                </div>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0;">
                    <strong>التحليل الشامل:</strong><br>
                    تحليل شامل للثغرة XSS مع التأثير: تنفيذ JavaScript في المتصفح
                </div>
            </div>
            
        </div>
        