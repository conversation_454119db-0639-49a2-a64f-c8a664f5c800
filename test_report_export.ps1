# Test Report Export Content - Check if exported reports contain comprehensive content
Write-Host "Testing EXPORTED REPORT CONTENT..." -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Results tracking
$results = @{
    ReportsFound = 0
    MainReportContent = 0
    SeparateReportContent = 0
    ComprehensiveContent = 0
    RealDataDisplay = 0
    Functions36Content = 0
    FilesContent = 0
    Score = 0
}

# Look for recently generated Bug Bounty reports
Write-Host "`nLooking for recently generated Bug Bounty reports..." -ForegroundColor Yellow

$reportFiles = Get-ChildItem -Path "." -Filter "*Bug_Bounty*" -File | Where-Object { $_.LastWriteTime -gt (Get-Date).AddHours(-1) }

if ($reportFiles.Count -eq 0) {
    Write-Host "No recent Bug Bounty reports found in current directory" -ForegroundColor Red
    Write-Host "Checking for any Bug Bounty files..." -ForegroundColor Yellow
    
    $allBugBountyFiles = Get-ChildItem -Path "." -Filter "*Bug_Bounty*" -File
    if ($allBugBountyFiles.Count -gt 0) {
        Write-Host "Found $($allBugBountyFiles.Count) Bug Bounty files (not recent):" -ForegroundColor Yellow
        foreach ($file in $allBugBountyFiles) {
            Write-Host "   $($file.Name) - Modified: $($file.LastWriteTime)" -ForegroundColor White
        }
        $reportFiles = $allBugBountyFiles | Select-Object -First 2
    } else {
        Write-Host "No Bug Bounty report files found at all" -ForegroundColor Red
        Write-Host "Reports may not be automatically saved to disk" -ForegroundColor Yellow
    }
}

if ($reportFiles.Count -gt 0) {
    Write-Host "Found $($reportFiles.Count) Bug Bounty report files to analyze:" -ForegroundColor Green
    $results.ReportsFound = $reportFiles.Count
    
    foreach ($file in $reportFiles) {
        Write-Host "`nAnalyzing: $($file.Name)" -ForegroundColor Cyan
        Write-Host "Size: $($file.Length) bytes" -ForegroundColor White
        Write-Host "Modified: $($file.LastWriteTime)" -ForegroundColor White
        
        try {
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            
            # Determine report type
            $isMainReport = $file.Name -match "main|comprehensive" -or $file.Length -gt 15000
            $reportType = if ($isMainReport) { "MAIN" } else { "SEPARATE" }
            Write-Host "Report Type: $reportType" -ForegroundColor Yellow
            
            # Check for comprehensive content indicators
            Write-Host "`nChecking comprehensive content..." -ForegroundColor Yellow
            $comprehensiveIndicators = @(
                "comprehensive_description",
                "technical_details",
                "exploitation_results",
                "impact_analysis",
                "security_implications",
                "business_impact"
            )
            
            $foundComprehensive = 0
            foreach ($indicator in $comprehensiveIndicators) {
                if ($content.Contains($indicator)) {
                    $foundComprehensive++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ $indicator" -ForegroundColor Red
                }
            }
            
            # Check for real data display modifications
            Write-Host "`nChecking real data display..." -ForegroundColor Yellow
            $realDataIndicators = @(
                "max-height: 200px; overflow-y: auto",
                "JSON.stringify",
                "typeof",
                "comprehensive_details",
                "dynamic_impact"
            )
            
            $foundRealData = 0
            foreach ($indicator in $realDataIndicators) {
                if ($content.Contains($indicator)) {
                    $foundRealData++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ $indicator" -ForegroundColor Red
                }
            }
            
            # Check for 36 functions content
            Write-Host "`nChecking 36 functions content..." -ForegroundColor Yellow
            $functionsIndicators = @(
                "generateComprehensiveDetailsFromRealData",
                "generateDynamicImpactForAnyVulnerability",
                "generateRealExploitationStepsForVulnerabilityComprehensive",
                "generateDynamicRecommendationsForVulnerability",
                "extractRealDataFromDiscoveredVulnerability"
            )
            
            $foundFunctions = 0
            foreach ($indicator in $functionsIndicators) {
                if ($content.Contains($indicator)) {
                    $foundFunctions++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ $indicator" -ForegroundColor Red
                }
            }
            
            # Check for system files content
            Write-Host "`nChecking system files content..." -ForegroundColor Yellow
            $filesIndicators = @(
                "impact_visualizer.js",
                "textual_impact_analyzer.js",
                "visual_impact_data",
                "textual_impact_analysis"
            )
            
            $foundFiles = 0
            foreach ($indicator in $filesIndicators) {
                if ($content.Contains($indicator)) {
                    $foundFiles++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ $indicator" -ForegroundColor Red
                }
            }
            
            # Summary for this report
            Write-Host "`nSummary for $reportType report:" -ForegroundColor White
            Write-Host "   Comprehensive content: $foundComprehensive/$($comprehensiveIndicators.Count)" -ForegroundColor White
            Write-Host "   Real data display: $foundRealData/$($realDataIndicators.Count)" -ForegroundColor White
            Write-Host "   Functions content: $foundFunctions/$($functionsIndicators.Count)" -ForegroundColor White
            Write-Host "   Files content: $foundFiles/$($filesIndicators.Count)" -ForegroundColor White
            
            # Update results
            if ($isMainReport) {
                $results.MainReportContent = $foundComprehensive + $foundRealData + $foundFunctions + $foundFiles
            } else {
                $results.SeparateReportContent = $foundComprehensive + $foundRealData + $foundFunctions + $foundFiles
            }
            
            $results.ComprehensiveContent += $foundComprehensive
            $results.RealDataDisplay += $foundRealData
            $results.Functions36Content += $foundFunctions
            $results.FilesContent += $foundFiles
            
        } catch {
            Write-Host "Error reading file: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "No Bug Bounty report files available for analysis" -ForegroundColor Red
    Write-Host "This means either:" -ForegroundColor Yellow
    Write-Host "   1. No reports have been generated yet" -ForegroundColor Yellow
    Write-Host "   2. Reports are generated but not saved to disk" -ForegroundColor Yellow
    Write-Host "   3. Reports are saved in a different location" -ForegroundColor Yellow
}

# Calculate score
Write-Host "`nCalculating final score..." -ForegroundColor Yellow

$reportsScore = if ($results.ReportsFound -gt 0) { 20 } else { 0 }
$comprehensiveScore = [math]::Min(($results.ComprehensiveContent / 12) * 30, 30)  # 6 indicators * 2 reports
$realDataScore = [math]::Min(($results.RealDataDisplay / 10) * 25, 25)  # 5 indicators * 2 reports
$functionsScore = [math]::Min(($results.Functions36Content / 10) * 15, 15)  # 5 indicators * 2 reports
$filesScore = [math]::Min(($results.FilesContent / 8) * 10, 10)  # 4 indicators * 2 reports

$results.Score = $reportsScore + $comprehensiveScore + $realDataScore + $functionsScore + $filesScore

# Final results
Write-Host "`nFINAL EXPORTED REPORT CONTENT RESULTS" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

Write-Host "Report Analysis:" -ForegroundColor White
Write-Host "   Reports Found: $($results.ReportsFound) ($reportsScore/20)" -ForegroundColor White
Write-Host "   Comprehensive Content: $($results.ComprehensiveContent)/12 ($([math]::Round($comprehensiveScore, 1))/30)" -ForegroundColor White
Write-Host "   Real Data Display: $($results.RealDataDisplay)/10 ($([math]::Round($realDataScore, 1))/25)" -ForegroundColor White
Write-Host "   Functions Content: $($results.Functions36Content)/10 ($([math]::Round($functionsScore, 1))/15)" -ForegroundColor White
Write-Host "   Files Content: $($results.FilesContent)/8 ($([math]::Round($filesScore, 1))/10)" -ForegroundColor White

Write-Host "`nTOTAL SCORE: $([math]::Round($results.Score, 1))/100" -ForegroundColor Yellow

if ($results.Score -ge 90) {
    Write-Host "EXCELLENT! Exported reports contain comprehensive detailed content!" -ForegroundColor Green
    Write-Host "✅ The 36 functions and system files are working in exported reports!" -ForegroundColor Green
} elseif ($results.Score -ge 70) {
    Write-Host "GOOD! Most comprehensive content is present in exported reports" -ForegroundColor Yellow
    Write-Host "🔧 Some improvements needed" -ForegroundColor Yellow
} elseif ($results.Score -ge 50) {
    Write-Host "PARTIAL! Some comprehensive content is present" -ForegroundColor Yellow
    Write-Host "⚠️ Significant improvements needed" -ForegroundColor Yellow
} elseif ($results.ReportsFound -eq 0) {
    Write-Host "NO REPORTS FOUND! Cannot test exported content" -ForegroundColor Red
    Write-Host "🚨 Generate reports first, then test again" -ForegroundColor Red
} else {
    Write-Host "NEEDS WORK! Exported reports lack comprehensive content" -ForegroundColor Red
    Write-Host "🚨 The 36 functions and system files are not working in exported reports" -ForegroundColor Red
}

# Specific recommendations
Write-Host "`nRecommendations:" -ForegroundColor Cyan

if ($results.ReportsFound -eq 0) {
    Write-Host "   1. Generate Bug Bounty reports first" -ForegroundColor Yellow
    Write-Host "   2. Check if reports are saved to disk automatically" -ForegroundColor Yellow
    Write-Host "   3. Look for reports in other directories" -ForegroundColor Yellow
} else {
    if ($results.ComprehensiveContent -lt 8) {
        Write-Host "   1. Ensure comprehensive functions generate real content" -ForegroundColor Yellow
    }
    if ($results.RealDataDisplay -lt 6) {
        Write-Host "   2. Fix real data display modifications" -ForegroundColor Yellow
    }
    if ($results.Functions36Content -lt 6) {
        Write-Host "   3. Ensure 36 functions are applied in reports" -ForegroundColor Yellow
    }
    if ($results.FilesContent -lt 4) {
        Write-Host "   4. Integrate system files properly in reports" -ForegroundColor Yellow
    }
}

$currentTime = Get-Date -Format 'HH:mm:ss'
Write-Host "`nExported report content test completed at $currentTime" -ForegroundColor Green
