# اختبار شامل للدوال الـ36 والملفات في نظام Bug Bounty v4.0
# للتحقق من سبب عدم عرض المحتوى الشامل التفصيلي

Write-Host "🔥 بدء اختبار الدوال الـ36 الشاملة التفصيلية..." -ForegroundColor Yellow

# التأكد من وجود الملفات المطلوبة
$bugBountyCoreFile = "assets/modules/bugbounty/BugBountyCore.js"
$testFile = "test_comprehensive_functions.js"

if (-not (Test-Path $bugBountyCoreFile)) {
    Write-Host "❌ ملف BugBountyCore.js غير موجود في: $bugBountyCoreFile" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $testFile)) {
    Write-Host "❌ ملف الاختبار غير موجود في: $testFile" -ForegroundColor Red
    exit 1
}

Write-Host "✅ الملفات المطلوبة موجودة" -ForegroundColor Green

# إنشاء ملف HTML للاختبار
$htmlTestContent = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدوال الـ36 الشاملة التفصيلية</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            direction: rtl; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            background: #f9f9f9;
        }
        .result { 
            background: #e8f5e8; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid #28a745;
        }
        .error { 
            background: #f8d7da; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid #dc3545;
        }
        .code { 
            background: #f1f3f4; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 300px; 
            overflow-y: auto;
        }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { 
            background: #0056b3; 
        }
        .loading { 
            color: #007bff; 
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار الدوال الـ36 الشاملة التفصيلية</h1>
        <p>هذا الاختبار يتحقق من سبب عدم عرض المحتوى الشامل التفصيلي في التقارير</p>
        
        <div class="test-section">
            <h2>🧪 تشغيل الاختبار</h2>
            <button onclick="runComprehensiveTest()">🚀 تشغيل اختبار الدوال الـ36</button>
            <button onclick="clearResults()">🧹 مسح النتائج</button>
            <div id="testStatus" class="loading" style="display: none;">⏳ جاري تشغيل الاختبار...</div>
        </div>
        
        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h2>🔍 تحليل المشكلة</h2>
            <div id="problemAnalysis"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 تفاصيل المحتوى المُنتج</h2>
            <div id="contentDetails"></div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="test_comprehensive_functions.js"></script>
    
    <script>
        async function runComprehensiveTest() {
            const statusDiv = document.getElementById('testStatus');
            const resultsDiv = document.getElementById('testResults');
            const analysisDiv = document.getElementById('problemAnalysis');
            const contentDiv = document.getElementById('contentDetails');
            
            // إظهار حالة التحميل
            statusDiv.style.display = 'block';
            resultsDiv.innerHTML = '';
            analysisDiv.innerHTML = '';
            contentDiv.innerHTML = '';
            
            try {
                console.log('🔥 بدء اختبار الدوال الـ36...');
                
                // تشغيل الاختبار
                const testResult = await testComprehensiveFunctions();
                
                // إخفاء حالة التحميل
                statusDiv.style.display = 'none';
                
                if (testResult.success) {
                    resultsDiv.innerHTML = '<div class="result">✅ الاختبار تم بنجاح!</div>';
                    
                    // تحليل النتائج
                    let analysis = '<h3>🔍 تحليل النتائج:</h3>';
                    
                    if (testResult.comprehensiveDetails && testResult.comprehensiveDetails.length > 50) {
                        analysis += '<div class="result">✅ generateComprehensiveDetailsFromRealData تعمل بشكل صحيح</div>';
                        analysis += '<div class="result">📏 طول المحتوى: ' + testResult.comprehensiveDetails.length + ' حرف</div>';
                    } else {
                        analysis += '<div class="error">❌ generateComprehensiveDetailsFromRealData لا تُنتج محتوى كافي</div>';
                    }
                    
                    if (testResult.comprehensiveAnalysis && testResult.comprehensiveAnalysis.length > 50) {
                        analysis += '<div class="result">✅ generateComprehensiveVulnerabilityAnalysis تعمل بشكل صحيح</div>';
                        analysis += '<div class="result">📏 طول المحتوى: ' + testResult.comprehensiveAnalysis.length + ' حرف</div>';
                    } else {
                        analysis += '<div class="error">❌ generateComprehensiveVulnerabilityAnalysis لا تُنتج محتوى كافي</div>';
                    }
                    
                    if (testResult.securityImpactAnalysis && testResult.securityImpactAnalysis.length > 50) {
                        analysis += '<div class="result">✅ generateDynamicSecurityImpactAnalysis تعمل بشكل صحيح</div>';
                        analysis += '<div class="result">📏 طول المحتوى: ' + testResult.securityImpactAnalysis.length + ' حرف</div>';
                    } else {
                        analysis += '<div class="error">❌ generateDynamicSecurityImpactAnalysis لا تُنتج محتوى كافي</div>';
                    }
                    
                    analysisDiv.innerHTML = analysis;
                    
                    // عرض تفاصيل المحتوى
                    let contentDetails = '<h3>📝 المحتوى المُنتج:</h3>';
                    
                    if (testResult.comprehensiveDetails) {
                        contentDetails += '<h4>🔍 generateComprehensiveDetailsFromRealData:</h4>';
                        contentDetails += '<div class="code">' + testResult.comprehensiveDetails.substring(0, 500) + '...</div>';
                    }
                    
                    if (testResult.comprehensiveAnalysis) {
                        contentDetails += '<h4>🔬 generateComprehensiveVulnerabilityAnalysis:</h4>';
                        contentDetails += '<div class="code">' + testResult.comprehensiveAnalysis.substring(0, 500) + '...</div>';
                    }
                    
                    if (testResult.securityImpactAnalysis) {
                        contentDetails += '<h4>🛡️ generateDynamicSecurityImpactAnalysis:</h4>';
                        contentDetails += '<div class="code">' + testResult.securityImpactAnalysis.substring(0, 500) + '...</div>';
                    }
                    
                    contentDiv.innerHTML = contentDetails;
                    
                } else {
                    resultsDiv.innerHTML = '<div class="error">❌ فشل الاختبار: ' + testResult.error + '</div>';
                }
                
            } catch (error) {
                statusDiv.style.display = 'none';
                resultsDiv.innerHTML = '<div class="error">❌ خطأ في تشغيل الاختبار: ' + error.message + '</div>';
                console.error('خطأ في الاختبار:', error);
            }
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('problemAnalysis').innerHTML = '';
            document.getElementById('contentDetails').innerHTML = '';
            document.getElementById('testStatus').style.display = 'none';
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🌐 صفحة الاختبار جاهزة');
        });
    </script>
</body>
</html>
"@

# حفظ ملف HTML
$htmlTestFile = "test_comprehensive_functions.html"
$htmlTestContent | Out-File -FilePath $htmlTestFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف الاختبار: $htmlTestFile" -ForegroundColor Green

# تشغيل الاختبار في المتصفح
Write-Host "🌐 فتح الاختبار في المتصفح..." -ForegroundColor Yellow
Start-Process $htmlTestFile

Write-Host "🎯 تم تشغيل اختبار الدوال الـ36 الشاملة التفصيلية" -ForegroundColor Green
Write-Host "📊 راجع النتائج في المتصفح لفهم سبب المشكلة" -ForegroundColor Cyan
