# Test Downloads Reports - Check exported reports in Downloads folder
Write-Host "Testing EXPORTED REPORTS in Downloads folder..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "Test Time: $timestamp" -ForegroundColor Green

# Results tracking
$results = @{
    ReportsFound = 0
    MainReports = 0
    SeparateReports = 0
    ComprehensiveContent = 0
    RealDataDisplay = 0
    Functions36Content = 0
    FilesContent = 0
    Score = 0
}

# Get Downloads folder path
$downloadsPath = [Environment]::GetFolderPath("UserProfile") + "\Downloads"
Write-Host "Downloads folder: $downloadsPath" -ForegroundColor White

# Look for Bug Bounty reports in Downloads
Write-Host "`nLooking for Bug Bounty reports in Downloads..." -ForegroundColor Yellow

$reportFiles = Get-ChildItem -Path $downloadsPath -Filter "*Bug*Bounty*" -File | Where-Object { $_.LastWriteTime -gt (Get-Date).AddHours(-24) }

if ($reportFiles.Count -eq 0) {
    Write-Host "No recent Bug Bounty reports found in Downloads" -ForegroundColor Red
    Write-Host "Checking for any Bug Bounty files..." -ForegroundColor Yellow
    
    $allBugBountyFiles = Get-ChildItem -Path $downloadsPath -Filter "*Bug*" -File | Where-Object { $_.Name -match "Bug.*Bounty|BugBounty" }
    if ($allBugBountyFiles.Count -gt 0) {
        Write-Host "Found $($allBugBountyFiles.Count) Bug Bounty files (any time):" -ForegroundColor Yellow
        foreach ($file in $allBugBountyFiles | Select-Object -First 5) {
            Write-Host "   $($file.Name) - Modified: $($file.LastWriteTime)" -ForegroundColor White
        }
        $reportFiles = $allBugBountyFiles | Select-Object -First 10
    } else {
        Write-Host "No Bug Bounty report files found in Downloads" -ForegroundColor Red
    }
}

if ($reportFiles.Count -gt 0) {
    Write-Host "Found $($reportFiles.Count) Bug Bounty report files to analyze:" -ForegroundColor Green
    $results.ReportsFound = $reportFiles.Count
    
    foreach ($file in $reportFiles) {
        Write-Host "`nAnalyzing: $($file.Name)" -ForegroundColor Cyan
        Write-Host "Size: $([math]::Round($file.Length/1024, 2)) KB" -ForegroundColor White
        Write-Host "Modified: $($file.LastWriteTime)" -ForegroundColor White
        
        try {
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            
            # Determine report type
            $isMainReport = $file.Name -match "main|comprehensive|Report_" -or $file.Length -gt 50000
            $reportType = if ($isMainReport) { "MAIN" } else { "SEPARATE" }
            Write-Host "Report Type: $reportType" -ForegroundColor Yellow
            
            if ($isMainReport) { $results.MainReports++ } else { $results.SeparateReports++ }
            
            # Check for comprehensive content indicators
            Write-Host "`nChecking comprehensive content..." -ForegroundColor Yellow
            $comprehensiveIndicators = @(
                "comprehensive_description",
                "technical_details",
                "exploitation_results", 
                "impact_analysis",
                "security_implications",
                "business_impact",
                "detailed_analysis",
                "vulnerability_assessment"
            )
            
            $foundComprehensive = 0
            foreach ($indicator in $comprehensiveIndicators) {
                if ($content.Contains($indicator)) {
                    $foundComprehensive++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                }
            }
            
            # Check for real data display modifications
            Write-Host "`nChecking real data display..." -ForegroundColor Yellow
            $realDataIndicators = @(
                "max-height: 200px; overflow-y: auto",
                "JSON.stringify",
                "typeof.*object",
                "comprehensive_details",
                "dynamic_impact",
                "exploitation_steps",
                "visual_impact_data"
            )
            
            $foundRealData = 0
            foreach ($indicator in $realDataIndicators) {
                if ($content -match [regex]::Escape($indicator)) {
                    $foundRealData++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                }
            }
            
            # Check for 36 functions content
            Write-Host "`nChecking 36 functions content..." -ForegroundColor Yellow
            $functionsIndicators = @(
                "generateComprehensiveDetailsFromRealData",
                "generateDynamicImpactForAnyVulnerability",
                "generateRealExploitationStepsForVulnerabilityComprehensive",
                "generateDynamicRecommendationsForVulnerability",
                "extractRealDataFromDiscoveredVulnerability",
                "generateComprehensiveVulnerabilityAnalysis",
                "generateDynamicSecurityImpactAnalysis"
            )
            
            $foundFunctions = 0
            foreach ($indicator in $functionsIndicators) {
                if ($content.Contains($indicator)) {
                    $foundFunctions++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                }
            }
            
            # Check for system files content
            Write-Host "`nChecking system files content..." -ForegroundColor Yellow
            $filesIndicators = @(
                "impact_visualizer.js",
                "textual_impact_analyzer.js",
                "visual_impact_data",
                "textual_impact_analysis",
                "ImpactVisualizer",
                "TextualImpactAnalyzer"
            )
            
            $foundFiles = 0
            foreach ($indicator in $filesIndicators) {
                if ($content.Contains($indicator)) {
                    $foundFiles++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                }
            }
            
            # Check for actual comprehensive content (not just function names)
            Write-Host "`nChecking actual comprehensive content..." -ForegroundColor Yellow
            $actualContentIndicators = @(
                "comprehensive.*analysis",
                "detailed.*assessment",
                "technical.*details",
                "exploitation.*results",
                "impact.*analysis",
                "security.*recommendations"
            )
            
            $foundActualContent = 0
            foreach ($indicator in $actualContentIndicators) {
                if ($content -match $indicator) {
                    $foundActualContent++
                    Write-Host "   ✅ $indicator" -ForegroundColor Green
                }
            }
            
            # Summary for this report
            Write-Host "`nSummary for $reportType report:" -ForegroundColor White
            Write-Host "   Comprehensive indicators: $foundComprehensive/$($comprehensiveIndicators.Count)" -ForegroundColor White
            Write-Host "   Real data display: $foundRealData/$($realDataIndicators.Count)" -ForegroundColor White
            Write-Host "   Functions content: $foundFunctions/$($functionsIndicators.Count)" -ForegroundColor White
            Write-Host "   Files content: $foundFiles/$($filesIndicators.Count)" -ForegroundColor White
            Write-Host "   Actual content: $foundActualContent/$($actualContentIndicators.Count)" -ForegroundColor White
            
            # Update results
            $results.ComprehensiveContent += $foundComprehensive
            $results.RealDataDisplay += $foundRealData
            $results.Functions36Content += $foundFunctions
            $results.FilesContent += $foundFiles
            
        } catch {
            Write-Host "Error reading file: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "No Bug Bounty report files found in Downloads" -ForegroundColor Red
    Write-Host "This means either:" -ForegroundColor Yellow
    Write-Host "   1. No reports have been generated yet" -ForegroundColor Yellow
    Write-Host "   2. Reports are saved in a different location" -ForegroundColor Yellow
    Write-Host "   3. Reports were generated more than 24 hours ago" -ForegroundColor Yellow
}

# Calculate score
Write-Host "`nCalculating final score..." -ForegroundColor Yellow

$reportsScore = if ($results.ReportsFound -gt 0) { 20 } else { 0 }
$mainReportsScore = if ($results.MainReports -gt 0) { 10 } else { 0 }
$separateReportsScore = if ($results.SeparateReports -gt 0) { 10 } else { 0 }

# Expected totals (assuming 2 reports: 1 main + 1 separate)
$expectedComprehensive = 16  # 8 indicators * 2 reports
$expectedRealData = 14       # 7 indicators * 2 reports
$expectedFunctions = 14      # 7 indicators * 2 reports
$expectedFiles = 12          # 6 indicators * 2 reports

$comprehensiveScore = [math]::Min(($results.ComprehensiveContent / $expectedComprehensive) * 20, 20)
$realDataScore = [math]::Min(($results.RealDataDisplay / $expectedRealData) * 15, 15)
$functionsScore = [math]::Min(($results.Functions36Content / $expectedFunctions) * 10, 10)
$filesScore = [math]::Min(($results.FilesContent / $expectedFiles) * 15, 15)

$results.Score = $reportsScore + $mainReportsScore + $separateReportsScore + $comprehensiveScore + $realDataScore + $functionsScore + $filesScore

# Final results
Write-Host "`nFINAL EXPORTED REPORTS ANALYSIS" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

Write-Host "Report Files:" -ForegroundColor White
Write-Host "   Total Reports Found: $($results.ReportsFound) ($reportsScore/20)" -ForegroundColor White
Write-Host "   Main Reports: $($results.MainReports) ($mainReportsScore/10)" -ForegroundColor White
Write-Host "   Separate Reports: $($results.SeparateReports) ($separateReportsScore/10)" -ForegroundColor White

Write-Host "`nContent Analysis:" -ForegroundColor White
Write-Host "   Comprehensive Content: $($results.ComprehensiveContent)/$expectedComprehensive ($([math]::Round($comprehensiveScore, 1))/20)" -ForegroundColor White
Write-Host "   Real Data Display: $($results.RealDataDisplay)/$expectedRealData ($([math]::Round($realDataScore, 1))/15)" -ForegroundColor White
Write-Host "   Functions Content: $($results.Functions36Content)/$expectedFunctions ($([math]::Round($functionsScore, 1))/10)" -ForegroundColor White
Write-Host "   Files Content: $($results.FilesContent)/$expectedFiles ($([math]::Round($filesScore, 1))/15)" -ForegroundColor White

Write-Host "`nTOTAL SCORE: $([math]::Round($results.Score, 1))/100" -ForegroundColor Yellow

if ($results.Score -ge 90) {
    Write-Host "EXCELLENT! Exported reports contain comprehensive detailed content!" -ForegroundColor Green
    Write-Host "✅ The 36 functions and system files are working perfectly in exported reports!" -ForegroundColor Green
} elseif ($results.Score -ge 70) {
    Write-Host "GOOD! Most comprehensive content is present in exported reports!" -ForegroundColor Yellow
    Write-Host "🔧 Some improvements needed for full comprehensive content" -ForegroundColor Yellow
} elseif ($results.Score -ge 50) {
    Write-Host "PARTIAL! Some comprehensive content is present!" -ForegroundColor Yellow
    Write-Host "⚠️ Significant improvements needed for comprehensive content" -ForegroundColor Yellow
} elseif ($results.ReportsFound -eq 0) {
    Write-Host "NO REPORTS FOUND! Cannot test exported content!" -ForegroundColor Red
    Write-Host "🚨 Generate Bug Bounty reports first, then test again" -ForegroundColor Red
} else {
    Write-Host "NEEDS WORK! Exported reports lack comprehensive detailed content!" -ForegroundColor Red
    Write-Host "🚨 The 36 functions and system files are not working properly in exported reports" -ForegroundColor Red
}

# Specific status
Write-Host "`nSpecific Status:" -ForegroundColor Cyan

if ($results.ReportsFound -eq 0) {
    Write-Host "   🚨 No reports found - generate reports first" -ForegroundColor Red
} else {
    Write-Host "   ✅ Reports found in Downloads folder" -ForegroundColor Green
    
    if ($results.MainReports -gt 0 -and $results.SeparateReports -gt 0) {
        Write-Host "   ✅ Both main and separate reports present" -ForegroundColor Green
    } elseif ($results.MainReports -gt 0) {
        Write-Host "   ⚠️ Only main reports found - separate reports missing" -ForegroundColor Yellow
    } elseif ($results.SeparateReports -gt 0) {
        Write-Host "   ⚠️ Only separate reports found - main reports missing" -ForegroundColor Yellow
    }
    
    if ($results.ComprehensiveContent -ge 12) {
        Write-Host "   ✅ Good comprehensive content in reports" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Insufficient comprehensive content in reports" -ForegroundColor Red
    }
    
    if ($results.RealDataDisplay -ge 10) {
        Write-Host "   ✅ Real data display modifications working" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Real data display modifications not working" -ForegroundColor Red
    }
    
    if ($results.Functions36Content -ge 10) {
        Write-Host "   ✅ 36 functions content present in reports" -ForegroundColor Green
    } else {
        Write-Host "   ❌ 36 functions content missing from reports" -ForegroundColor Red
    }
    
    if ($results.FilesContent -ge 8) {
        Write-Host "   ✅ System files content present in reports" -ForegroundColor Green
    } else {
        Write-Host "   ❌ System files content missing from reports" -ForegroundColor Red
    }
}

$currentTime = Get-Date -Format 'HH:mm:ss'
Write-Host "`nDownloads reports test completed at $currentTime" -ForegroundColor Green
